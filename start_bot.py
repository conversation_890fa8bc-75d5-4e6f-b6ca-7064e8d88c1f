#!/usr/bin/env python3
"""
AI Trading Bot Startup Script
"""
import sys
import os
import time
from datetime import datetime

def check_dependencies():
    """Check if all required dependencies are installed"""
    required_packages = [
        'MetaTrader5',
        'pandas',
        'numpy',
        'requests',
        'python-dotenv',
        'schedule'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nPlease install missing packages:")
        print("pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies are installed")
    return True

def check_configuration():
    """Check if configuration file exists and is valid"""
    if not os.path.exists('.env'):
        print("❌ Configuration file (.env) not found")
        print("Please copy and configure the .env file with your settings")
        return False
    
    # Import config to validate
    try:
        from config import Config
        if not Config.validate_config():
            print("❌ Configuration validation failed")
            print("Please check your .env file settings")
            return False
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False
    
    print("✅ Configuration is valid")
    return True

def check_mt5_terminal():
    """Check if MT5 terminal is accessible"""
    try:
        import MetaTrader5 as mt5
        if not mt5.initialize():
            print("❌ Cannot connect to MetaTrader 5")
            print("Please ensure MT5 terminal is running and accessible")
            return False
        
        mt5.shutdown()
        print("✅ MetaTrader 5 is accessible")
        return True
        
    except Exception as e:
        print(f"❌ MT5 connection error: {e}")
        return False

def display_startup_info():
    """Display startup information"""
    from config import Config
    
    print("\n" + "="*60)
    print("🤖 AI TRADING BOT")
    print("="*60)
    print(f"📅 Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📊 Trading Pairs: {', '.join(Config.TRADING_PAIRS)}")
    print(f"🎯 Min Confluence Score: {Config.MIN_CONFLUENCE_SCORE}")
    print(f"💰 Max Risk Per Trade: {Config.MAX_RISK_PER_TRADE}%")
    print(f"🛡️  Max Daily Loss: {Config.MAX_DAILY_LOSS}%")
    print(f"📝 Paper Trading: {'✅ ENABLED' if Config.PAPER_TRADING else '❌ DISABLED (REAL MONEY)'}")
    print("="*60)
    
    if not Config.PAPER_TRADING:
        print("⚠️  WARNING: REAL TRADING MODE ENABLED!")
        print("⚠️  This bot will trade with real money!")
        print("⚠️  Ensure you understand the risks involved!")
        print("="*60)
        
        response = input("Continue with real trading? (type 'YES' to confirm): ")
        if response != 'YES':
            print("Startup cancelled by user")
            return False
    
    return True

def run_pre_flight_checks():
    """Run comprehensive pre-flight checks"""
    print("🔍 Running pre-flight checks...\n")
    
    checks = [
        ("Dependencies", check_dependencies),
        ("Configuration", check_configuration),
        ("MT5 Terminal", check_mt5_terminal)
    ]
    
    for check_name, check_func in checks:
        print(f"Checking {check_name}...")
        if not check_func():
            print(f"\n❌ Pre-flight check failed: {check_name}")
            return False
        time.sleep(0.5)  # Small delay for better UX
    
    print("\n✅ All pre-flight checks passed!")
    return True

def start_trading_bot():
    """Start the main trading bot"""
    try:
        print("\n🚀 Starting AI Trading Bot...")
        
        # Import and start the main bot
        from main import TradingBot
        
        bot = TradingBot()
        bot.start()
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Bot stopped by user")
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        sys.exit(1)

def main():
    """Main startup function"""
    print("🤖 AI Trading Bot - Startup Sequence")
    print("="*40)
    
    # Run pre-flight checks
    if not run_pre_flight_checks():
        print("\n❌ Startup aborted due to failed checks")
        sys.exit(1)
    
    # Display startup information and get confirmation
    if not display_startup_info():
        sys.exit(0)
    
    # Final confirmation
    print("\n🚀 Ready to start trading bot!")
    input("Press Enter to continue or Ctrl+C to cancel...")
    
    # Start the bot
    start_trading_bot()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 Startup error: {e}")
        sys.exit(1)
