"""
Logging system for the AI Trading Bot
"""
import logging
import os
from datetime import datetime
from typing import Optional
from config import Config

class TradingLogger:
    """Enhanced logging system for trading bot operations"""
    
    def __init__(self, name: str = "TradingBot", log_file: Optional[str] = None):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, Config.LOG_LEVEL.upper()))
        
        # Prevent duplicate handlers
        if not self.logger.handlers:
            self._setup_handlers(log_file or Config.LOG_FILE)
    
    def _setup_handlers(self, log_file: str):
        """Setup file and console handlers"""
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # File handler
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    def info(self, message: str):
        """Log info message"""
        self.logger.info(message)
    
    def debug(self, message: str):
        """Log debug message"""
        self.logger.debug(message)
    
    def warning(self, message: str):
        """Log warning message"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """Log error message"""
        self.logger.error(message)
    
    def critical(self, message: str):
        """Log critical message"""
        self.logger.critical(message)
    
    def log_trade(self, action: str, symbol: str, lot_size: float, 
                  price: float, sl: float, tp: float, **kwargs):
        """Log trade-specific information"""
        trade_info = {
            'action': action,
            'symbol': symbol,
            'lot_size': lot_size,
            'price': price,
            'sl': sl,
            'tp': tp,
            'timestamp': datetime.now().isoformat(),
            **kwargs
        }
        self.info(f"TRADE: {trade_info}")
    
    def log_signal(self, symbol: str, confluence_score: float, 
                   factors: list, decision: str, **kwargs):
        """Log signal detection information"""
        signal_info = {
            'symbol': symbol,
            'confluence_score': confluence_score,
            'factors': factors,
            'decision': decision,
            'timestamp': datetime.now().isoformat(),
            **kwargs
        }
        self.info(f"SIGNAL: {signal_info}")
    
    def log_ai_request(self, symbol: str, input_tokens: int, 
                       output_tokens: int, cost: float, decision: str):
        """Log AI API request information"""
        ai_info = {
            'symbol': symbol,
            'input_tokens': input_tokens,
            'output_tokens': output_tokens,
            'cost': cost,
            'decision': decision,
            'timestamp': datetime.now().isoformat()
        }
        self.info(f"AI_REQUEST: {ai_info}")
    
    def log_performance(self, symbol: str, pnl: float, 
                       win_rate: float, total_trades: int):
        """Log performance metrics"""
        perf_info = {
            'symbol': symbol,
            'pnl': pnl,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'timestamp': datetime.now().isoformat()
        }
        self.info(f"PERFORMANCE: {perf_info}")
    
    def log_error_with_context(self, error: Exception, context: dict):
        """Log error with additional context"""
        error_info = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context,
            'timestamp': datetime.now().isoformat()
        }
        self.error(f"ERROR_CONTEXT: {error_info}")

# Global logger instance
logger = TradingLogger()

# Convenience functions
def log_info(message: str):
    logger.info(message)

def log_debug(message: str):
    logger.debug(message)

def log_warning(message: str):
    logger.warning(message)

def log_error(message: str):
    logger.error(message)

def log_critical(message: str):
    logger.critical(message)

def log_trade(action: str, symbol: str, lot_size: float, 
              price: float, sl: float, tp: float, **kwargs):
    logger.log_trade(action, symbol, lot_size, price, sl, tp, **kwargs)

def log_signal(symbol: str, confluence_score: float, 
               factors: list, decision: str, **kwargs):
    logger.log_signal(symbol, confluence_score, factors, decision, **kwargs)

def log_ai_request(symbol: str, input_tokens: int, 
                   output_tokens: int, cost: float, decision: str):
    logger.log_ai_request(symbol, input_tokens, output_tokens, cost, decision)

def log_performance(symbol: str, pnl: float, win_rate: float, total_trades: int):
    logger.log_performance(symbol, pnl, win_rate, total_trades)

def log_error_with_context(error: Exception, context: dict):
    logger.log_error_with_context(error, context)
