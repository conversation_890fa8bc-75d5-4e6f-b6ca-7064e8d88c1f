# AI Trading Bot Development Project

You are an expert Python developer specializing in algorithmic trading systems. I need you to build a complete AI-powered trading bot with the following specifications:

## Project Overview

**Objective:** Create an automated trading system that uses Smart Money Concepts (SMC), Supply & Demand (SND), and Support & Resistance (SNR) analysis to generate trading signals, then sends only high-confluence setups to an AI agent (DeepSeek) for final trading decisions.

**Architecture:** Python Script → MetaTrader5 Library → SMC Analysis → AI Agent → Trade Execution

## Technical Requirements

### Core Libraries to Use:
```python
import MetaTrader5 as mt5           # Direct MT5 connection
import pandas as pd                 # Data processing
from smartmoneyconcepts import smc  # SMC analysis library
import requests                     # API calls to DeepSeek
import json                        # JSON handling
import time                        # Scheduling
import logging                     # Error tracking
```

### Development Phases:

## Phase 1: MT5 Connection & Data Pipeline
**Duration: Week 1**

**Requirements:**
1. **MT5 Setup Function**
   - Initialize MT5 connection
   - Handle connection errors
   - Login validation
   - Symbol availability check

2. **Data Retrieval System**
   - Multi-timeframe OHLCV data (M15, H1, H4, D1)
   - Real-time price feeds
   - Account information (balance, equity, positions)
   - Error handling for data gaps

3. **Basic Logging System**
   - Connection status
   - Data retrieval logs
   - Error tracking
   - Performance metrics

**Expected Deliverable:** Working MT5 connection with reliable data retrieval

## Phase 2: SMC Analysis Engine
**Duration: Week 1**

**Requirements:**
1. **Smart Money Concepts Implementation**
   ```python
   # Use smartmoneyconcepts library for:
   - Market Structure Analysis (BOS/CHoCH detection)
   - Order Blocks identification (Bullish/Bearish)
   - Fair Value Gaps detection
   - Premium/Discount zones
   - Liquidity levels (Equal highs/lows)
   ```

2. **Supply & Demand Zones**
   - Rally-Base-Drop patterns (Supply)
   - Drop-Base-Rally patterns (Demand)
   - Zone strength scoring (1-10)
   - Fresh vs tested zone classification

3. **Support & Resistance Levels**
   - Multi-timeframe S/R detection
   - Psychological levels (00, 50)
   - Dynamic level strength
   - Role reversal tracking

**Expected Deliverable:** Complete SMC/SND/SNR analysis functions

## Phase 3: Signal Detection & Confluence Scoring
**Duration: Week 1**

**Requirements:**
1. **Local Signal Pre-Filtering**
   ```python
   def detect_confluence_signals():
       # Only trigger AI when confluence score >= 7.5
       # Minimum 3 confluence factors required
       # Filter by session timing and news proximity
   ```

2. **Confluence Scoring Algorithm**
   - Weight each factor by strength (1-10)
   - Timeframe alignment bonus
   - Fresh zone premiums
   - Distance penalty calculation

3. **Risk Pre-Assessment**
   - Account balance checks
   - Daily loss limits
   - Position correlation analysis
   - News calendar integration

**Expected Deliverable:** Smart signal detection that minimizes AI API calls

## Phase 4: AI Agent Integration
**Duration: Week 1**

**Requirements:**
1. **DeepSeek API Integration**
   ```python
   # Optimized JSON request format (target: <700 tokens input)
   # Expected response format (target: <100 tokens output)
   # Error handling for API failures
   # Rate limiting and retry logic
   ```

2. **Prompt Optimization**
   - Concise signal description
   - Clear decision requirements
   - Structured response format
   - Token efficiency focus

3. **Response Processing**
   - Parse AI decision (TRADE/NO_TRADE)
   - Extract trade parameters
   - Validate response format
   - Handle AI "hallucinations"

**Expected Deliverable:** Reliable AI decision-making system with ultra-low token costs

## Phase 5: Trade Execution & Risk Management
**Duration: Week 1**

**Requirements:**
1. **Automated Order Execution**
   ```python
   # Limit order placement
   # Stop loss and take profit settings
   # Partial close functionality
   # Order modification capabilities
   ```

2. **Risk Management System**
   - Position sizing based on account percentage
   - Correlation risk monitoring
   - Daily/weekly loss limits
   - Emergency stop mechanisms

3. **Trade Monitoring**
   - Real-time P&L tracking
   - Structure invalidation alerts
   - News-based exit triggers
   - Performance logging

**Expected Deliverable:** Complete automated trading system

## Phase 6: Testing & Optimization
**Duration: Week 1**

**Requirements:**
1. **Paper Trading Mode**
   - Simulate all trades without real money
   - Track hypothetical performance
   - Log all decisions and outcomes

2. **Performance Analytics**
   - Win rate calculation
   - Risk-reward ratios
   - Drawdown analysis
   - Signal quality metrics

3. **System Optimization**
   - Fine-tune confluence scoring
   - Optimize AI prompt efficiency
   - Reduce latency bottlenecks

**Expected Deliverable:** Fully tested system ready for live trading

## Specific Code Requirements

### 1. Configuration Management
```python
CONFIG = {
    "MT5": {
        "login": 123456,
        "password": "password",
        "server": "Server-Name"
    },
    "RISK": {
        "max_risk_per_trade": 2.0,
        "max_daily_loss": 5.0,
        "max_positions": 3
    },
    "AI": {
        "api_key": "deepseek_api_key",
        "model": "deepseek-v3",
        "max_tokens": 100
    }
}
```

### 2. Signal JSON Format (Optimized)
```python
signal_format = {
    "symbol": "EURUSD",
    "confluence": {"score": 8.5, "factors": [...]},
    "structure": {"M15": "BULLISH_BOS", "H1": "BULLISH_BOS"},
    "levels": {"entry": [1.0541], "sl": [1.0532], "tp": [1.0555, 1.0565]},
    "risk": {"correlation": 0.78, "news_minutes": 67}
}
```

### 3. Expected AI Response Format
```python
ai_response = {
    "decision": "TRADE",
    "confidence": 0.85,
    "entry": 1.0541,
    "sl": 1.0532,
    "tp1": 1.0555,
    "tp2": 1.0565,
    "lot_size": 0.18,
    "reasoning": "Strong confluence with fresh H1 OB and FVG alignment"
}
```

## Performance Targets

### Cost Efficiency:
- **Target:** <$0.05/month for AI costs (6-8 signals/day)
- **Token usage:** <700 input, <100 output per request
- **API calls:** Only when confluence score ≥ 7.5

### Trading Performance:
- **Win rate target:** >65%
- **Risk-reward:** >2:1 average
- **Max drawdown:** <10%
- **Signal frequency:** 6-8 high-quality signals/day

### System Performance:
- **Latency:** <5 seconds from signal to execution
- **Uptime:** >99%
- **Error rate:** <1%

## Code Structure Requirements

```
trading_bot/
├── main.py                 # Main execution script
├── mt5_connector.py        # MT5 connection handling
├── smc_analyzer.py         # SMC/SND/SNR analysis
├── signal_detector.py      # Confluence scoring
├── ai_agent.py            # DeepSeek API integration
├── trade_executor.py      # Order management
├── risk_manager.py        # Risk controls
├── config.py              # Configuration settings
├── logger.py              # Logging system
└── utils.py               # Helper functions
```

## Development Guidelines

1. **Error Handling:** Every function must have comprehensive error handling
2. **Logging:** Log all decisions, API calls, trades, and errors
3. **Documentation:** Clear docstrings for all functions
4. **Testing:** Include unit tests for critical functions
5. **Configuration:** All parameters should be configurable
6. **Efficiency:** Optimize for speed and low resource usage

## Success Criteria

**Phase Completion:**
- Each phase must be fully functional before proceeding
- Code must pass basic testing
- Documentation must be complete

**Final System:**
- Successfully connects to MT5
- Accurately detects SMC signals
- Efficiently communicates with AI agent
- Executes trades with proper risk management
- Achieves target cost efficiency (<$0.05/month)

## Additional Requirements

1. **Scalability:** Code should support multiple currency pairs
2. **Maintainability:** Clean, modular code structure
3. **Monitoring:** Built-in performance monitoring
4. **Safety:** Multiple safeguards against excessive losses
5. **Flexibility:** Easy to modify parameters and strategies

## Notes for AI Agent Development

- Focus on **token efficiency** - every token costs money
- Prioritize **reliability** over complexity
- Implement **graceful degradation** for API failures
- Use **defensive programming** practices
- Test thoroughly with **paper trading** before live deployment

Build this system incrementally, ensuring each phase is solid before moving to the next. The goal is a production-ready automated trading system that combines institutional-level analysis with cost-effective AI decision making.

Start with Phase 1 and provide complete, working code for the MT5 connection and data pipeline.
