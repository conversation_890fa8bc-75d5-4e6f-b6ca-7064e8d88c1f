"""
Configuration management for the AI Trading Bot
"""
import os
from dotenv import load_dotenv
from typing import List, Dict, Any

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for trading bot settings"""
    
    # MetaTrader 5 Configuration
    MT5_LOGIN = int(os.getenv('MT5_LOGIN', 0))
    MT5_PASSWORD = os.getenv('MT5_PASSWORD', '')
    MT5_SERVER = os.getenv('MT5_SERVER', '')
    
    # Trading Configuration
    TRADING_PAIRS = os.getenv('TRADING_PAIRS', 'EURUSD,GBPUSD').split(',')
    TIMEFRAMES = os.getenv('TIMEFRAMES', 'M15,H1,H4,D1').split(',')
    
    # AI Agent Configuration
    DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY', '')
    DEEPSEEK_MODEL = os.getenv('DEEPSEEK_MODEL', 'deepseek-v3')
    DEEPSEEK_MAX_TOKENS = int(os.getenv('DEEPSEEK_MAX_TOKENS', 100))
    DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions"
    
    # Risk Management
    MAX_RISK_PER_TRADE = float(os.getenv('MAX_RISK_PER_TRADE', 2.0))
    MAX_DAILY_LOSS = float(os.getenv('MAX_DAILY_LOSS', 5.0))
    MAX_POSITIONS = int(os.getenv('MAX_POSITIONS', 3))
    ACCOUNT_BALANCE_PERCENTAGE = float(os.getenv('ACCOUNT_BALANCE_PERCENTAGE', 1.0))
    
    # Signal Configuration
    MIN_CONFLUENCE_SCORE = float(os.getenv('MIN_CONFLUENCE_SCORE', 7.5))
    MIN_CONFLUENCE_FACTORS = int(os.getenv('MIN_CONFLUENCE_FACTORS', 3))
    
    # Logging Configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'trading_bot.log')
    
    # Trading Mode
    PAPER_TRADING = os.getenv('PAPER_TRADING', 'True').lower() == 'true'
    
    # Session Times (UTC)
    LONDON_OPEN = os.getenv('LONDON_OPEN', '08:00')
    LONDON_CLOSE = os.getenv('LONDON_CLOSE', '17:00')
    NEW_YORK_OPEN = os.getenv('NEW_YORK_OPEN', '13:00')
    NEW_YORK_CLOSE = os.getenv('NEW_YORK_CLOSE', '22:00')
    
    # News API
    NEWS_API_KEY = os.getenv('NEWS_API_KEY', '')
    
    @classmethod
    def get_mt5_config(cls) -> Dict[str, Any]:
        """Get MT5 connection configuration"""
        return {
            'login': cls.MT5_LOGIN,
            'password': cls.MT5_PASSWORD,
            'server': cls.MT5_SERVER
        }
    
    @classmethod
    def get_risk_config(cls) -> Dict[str, Any]:
        """Get risk management configuration"""
        return {
            'max_risk_per_trade': cls.MAX_RISK_PER_TRADE,
            'max_daily_loss': cls.MAX_DAILY_LOSS,
            'max_positions': cls.MAX_POSITIONS,
            'account_balance_percentage': cls.ACCOUNT_BALANCE_PERCENTAGE
        }
    
    @classmethod
    def get_ai_config(cls) -> Dict[str, Any]:
        """Get AI agent configuration"""
        return {
            'api_key': cls.DEEPSEEK_API_KEY,
            'model': cls.DEEPSEEK_MODEL,
            'max_tokens': cls.DEEPSEEK_MAX_TOKENS,
            'api_url': cls.DEEPSEEK_API_URL
        }
    
    @classmethod
    def validate_config(cls) -> bool:
        """Validate essential configuration parameters"""
        required_fields = [
            cls.MT5_LOGIN,
            cls.MT5_PASSWORD,
            cls.MT5_SERVER,
            cls.DEEPSEEK_API_KEY
        ]
        
        missing_fields = []
        if cls.MT5_LOGIN == 0:
            missing_fields.append('MT5_LOGIN')
        if not cls.MT5_PASSWORD:
            missing_fields.append('MT5_PASSWORD')
        if not cls.MT5_SERVER:
            missing_fields.append('MT5_SERVER')
        if not cls.DEEPSEEK_API_KEY:
            missing_fields.append('DEEPSEEK_API_KEY')
            
        if missing_fields:
            print(f"Missing required configuration: {', '.join(missing_fields)}")
            return False
        
        return True

# MT5 Timeframe mappings
MT5_TIMEFRAMES = {
    'M1': 1,
    'M5': 5,
    'M15': 15,
    'M30': 30,
    'H1': 16385,
    'H4': 16388,
    'D1': 16408,
    'W1': 32769,
    'MN1': 49153
}

# Currency pair specifications
PAIR_SPECS = {
    'EURUSD': {'pip_value': 0.0001, 'digits': 5},
    'GBPUSD': {'pip_value': 0.0001, 'digits': 5},
    'USDJPY': {'pip_value': 0.01, 'digits': 3},
    'AUDUSD': {'pip_value': 0.0001, 'digits': 5},
    'USDCAD': {'pip_value': 0.0001, 'digits': 5},
    'USDCHF': {'pip_value': 0.0001, 'digits': 5},
    'NZDUSD': {'pip_value': 0.0001, 'digits': 5},
    'EURGBP': {'pip_value': 0.0001, 'digits': 5},
    'EURJPY': {'pip_value': 0.01, 'digits': 3},
    'GBPJPY': {'pip_value': 0.01, 'digits': 3}
}
