"""
Performance Analytics and Reporting
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, <PERSON><PERSON>
from datetime import datetime, timedelta
import json
from logger import log_info, log_error
from trade_executor import trade_executor
from ai_agent import ai_agent

class PerformanceAnalytics:
    """Performance analytics and reporting system"""
    
    def __init__(self):
        self.analytics_cache = {}
        self.benchmark_data = {}
    
    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'summary': self._generate_summary(),
                'trading_performance': self._analyze_trading_performance(),
                'ai_performance': self._analyze_ai_performance(),
                'risk_metrics': self._calculate_risk_metrics(),
                'efficiency_metrics': self._calculate_efficiency_metrics(),
                'recommendations': self._generate_recommendations()
            }
            
            return report
            
        except Exception as e:
            log_error(f"Error generating comprehensive report: {e}")
            return {'error': str(e)}
    
    def _generate_summary(self) -> Dict[str, Any]:
        """Generate performance summary"""
        try:
            trade_stats = trade_executor.get_trade_statistics()
            ai_stats = ai_agent.get_usage_stats()
            
            summary = {
                'total_trades': trade_stats.get('total_trades', 0),
                'win_rate': trade_stats.get('win_rate', 0),
                'total_pips': trade_stats.get('total_pips', 0),
                'profit_factor': trade_stats.get('profit_factor', 0),
                'ai_requests': ai_stats.get('total_requests', 0),
                'ai_cost': ai_stats.get('total_cost', 0),
                'cost_per_trade': ai_stats.get('total_cost', 0) / max(1, trade_stats.get('total_trades', 1)),
                'active_positions': len(trade_executor.active_trades)
            }
            
            return summary
            
        except Exception as e:
            log_error(f"Error generating summary: {e}")
            return {}
    
    def _analyze_trading_performance(self) -> Dict[str, Any]:
        """Analyze detailed trading performance"""
        try:
            if not trade_executor.trade_history:
                return {'message': 'No trade history available'}
            
            trades_df = pd.DataFrame(trade_executor.trade_history)
            
            # Basic metrics
            total_trades = len(trades_df)
            winning_trades = len(trades_df[trades_df['pnl_pips'] > 0])
            losing_trades = len(trades_df[trades_df['pnl_pips'] < 0])
            
            # Performance metrics
            win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
            
            # Pip analysis
            total_pips = trades_df['pnl_pips'].sum()
            avg_win = trades_df[trades_df['pnl_pips'] > 0]['pnl_pips'].mean() if winning_trades > 0 else 0
            avg_loss = trades_df[trades_df['pnl_pips'] < 0]['pnl_pips'].mean() if losing_trades > 0 else 0
            
            # Risk-reward analysis
            profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else 0
            
            # Streak analysis
            win_streak, loss_streak = self._calculate_streaks(trades_df['pnl_pips'].tolist())
            
            # Time analysis
            if 'open_time' in trades_df.columns and 'close_time' in trades_df.columns:
                trades_df['duration'] = pd.to_datetime(trades_df['close_time']) - pd.to_datetime(trades_df['open_time'])
                avg_duration = trades_df['duration'].mean()
            else:
                avg_duration = timedelta(0)
            
            # Symbol performance
            symbol_performance = {}
            if 'symbol' in trades_df.columns:
                for symbol in trades_df['symbol'].unique():
                    symbol_trades = trades_df[trades_df['symbol'] == symbol]
                    symbol_performance[symbol] = {
                        'trades': len(symbol_trades),
                        'win_rate': (len(symbol_trades[symbol_trades['pnl_pips'] > 0]) / len(symbol_trades)) * 100,
                        'total_pips': symbol_trades['pnl_pips'].sum()
                    }
            
            return {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': round(win_rate, 2),
                'total_pips': round(total_pips, 1),
                'average_win': round(avg_win, 1),
                'average_loss': round(avg_loss, 1),
                'profit_factor': round(profit_factor, 2),
                'max_win_streak': win_streak,
                'max_loss_streak': loss_streak,
                'average_duration': str(avg_duration),
                'symbol_performance': symbol_performance
            }
            
        except Exception as e:
            log_error(f"Error analyzing trading performance: {e}")
            return {'error': str(e)}
    
    def _analyze_ai_performance(self) -> Dict[str, Any]:
        """Analyze AI agent performance"""
        try:
            ai_stats = ai_agent.get_usage_stats()
            
            # Calculate AI accuracy (simplified)
            ai_accuracy = self._calculate_ai_accuracy()
            
            # Cost efficiency
            cost_per_request = ai_stats.get('average_cost_per_request', 0)
            monthly_cost_estimate = ai_stats.get('estimated_monthly_cost', 0)
            
            # Token efficiency
            avg_tokens_per_request = self._estimate_avg_tokens()
            
            return {
                'total_requests': ai_stats.get('total_requests', 0),
                'total_cost': round(ai_stats.get('total_cost', 0), 6),
                'cost_per_request': round(cost_per_request, 6),
                'monthly_cost_estimate': round(monthly_cost_estimate, 2),
                'ai_accuracy': round(ai_accuracy, 2),
                'avg_tokens_per_request': avg_tokens_per_request,
                'cost_efficiency_rating': self._rate_cost_efficiency(monthly_cost_estimate)
            }
            
        except Exception as e:
            log_error(f"Error analyzing AI performance: {e}")
            return {'error': str(e)}
    
    def _calculate_risk_metrics(self) -> Dict[str, Any]:
        """Calculate risk metrics"""
        try:
            if not trade_executor.trade_history:
                return {'message': 'No trade history for risk analysis'}
            
            trades_df = pd.DataFrame(trade_executor.trade_history)
            pnl_series = trades_df['pnl_pips']
            
            # Drawdown analysis
            cumulative_pnl = pnl_series.cumsum()
            running_max = cumulative_pnl.expanding().max()
            drawdown = cumulative_pnl - running_max
            max_drawdown = drawdown.min()
            
            # Volatility metrics
            pnl_std = pnl_series.std()
            sharpe_ratio = pnl_series.mean() / pnl_std if pnl_std != 0 else 0
            
            # Risk of ruin (simplified)
            win_rate = len(pnl_series[pnl_series > 0]) / len(pnl_series) * 100
            avg_win = pnl_series[pnl_series > 0].mean() if len(pnl_series[pnl_series > 0]) > 0 else 0
            avg_loss = abs(pnl_series[pnl_series < 0].mean()) if len(pnl_series[pnl_series < 0]) > 0 else 0
            
            risk_of_ruin = self._calculate_risk_of_ruin(win_rate, avg_win, avg_loss)
            
            return {
                'max_drawdown_pips': round(max_drawdown, 1),
                'volatility': round(pnl_std, 2),
                'sharpe_ratio': round(sharpe_ratio, 2),
                'risk_of_ruin': round(risk_of_ruin, 2),
                'consecutive_losses': self._calculate_streaks(pnl_series.tolist())[1],
                'risk_rating': self._rate_risk_level(max_drawdown, sharpe_ratio)
            }
            
        except Exception as e:
            log_error(f"Error calculating risk metrics: {e}")
            return {'error': str(e)}
    
    def _calculate_efficiency_metrics(self) -> Dict[str, Any]:
        """Calculate system efficiency metrics"""
        try:
            # Signal to trade conversion rate
            signal_count = self._estimate_signal_count()
            trade_count = trade_executor.get_trade_statistics().get('total_trades', 0)
            conversion_rate = (trade_count / signal_count * 100) if signal_count > 0 else 0
            
            # AI decision accuracy
            ai_accuracy = self._calculate_ai_accuracy()
            
            # Cost per profitable trade
            profitable_trades = len([t for t in trade_executor.trade_history if t.get('pnl_pips', 0) > 0])
            ai_cost = ai_agent.get_usage_stats().get('total_cost', 0)
            cost_per_profitable_trade = ai_cost / max(1, profitable_trades)
            
            return {
                'signal_to_trade_conversion': round(conversion_rate, 1),
                'ai_decision_accuracy': round(ai_accuracy, 1),
                'cost_per_profitable_trade': round(cost_per_profitable_trade, 4),
                'system_uptime': self._calculate_uptime(),
                'efficiency_rating': self._rate_efficiency(conversion_rate, ai_accuracy)
            }
            
        except Exception as e:
            log_error(f"Error calculating efficiency metrics: {e}")
            return {'error': str(e)}
    
    def _generate_recommendations(self) -> List[str]:
        """Generate performance improvement recommendations"""
        recommendations = []
        
        try:
            trade_stats = trade_executor.get_trade_statistics()
            ai_stats = ai_agent.get_usage_stats()
            
            # Win rate recommendations
            win_rate = trade_stats.get('win_rate', 0)
            if win_rate < 60:
                recommendations.append("Consider increasing minimum confluence score to improve win rate")
            
            # Profit factor recommendations
            profit_factor = trade_stats.get('profit_factor', 0)
            if profit_factor < 1.5:
                recommendations.append("Review risk-reward ratios and exit strategies")
            
            # Cost efficiency recommendations
            monthly_cost = ai_stats.get('estimated_monthly_cost', 0)
            if monthly_cost > 5:  # More than $5/month
                recommendations.append("Optimize AI prompts to reduce token usage and costs")
            
            # Trade frequency recommendations
            total_trades = trade_stats.get('total_trades', 0)
            if total_trades < 10:
                recommendations.append("Consider lowering confluence threshold to increase trade frequency")
            elif total_trades > 50:
                recommendations.append("High trade frequency detected - ensure quality over quantity")
            
            # Risk recommendations
            if not recommendations:  # If no specific issues found
                recommendations.append("Performance looks good - continue monitoring and fine-tuning")
            
            return recommendations
            
        except Exception as e:
            log_error(f"Error generating recommendations: {e}")
            return ["Error generating recommendations"]
    
    def _calculate_streaks(self, pnl_list: List[float]) -> Tuple[int, int]:
        """Calculate maximum winning and losing streaks"""
        if not pnl_list:
            return 0, 0
        
        max_win_streak = 0
        max_loss_streak = 0
        current_win_streak = 0
        current_loss_streak = 0
        
        for pnl in pnl_list:
            if pnl > 0:
                current_win_streak += 1
                current_loss_streak = 0
                max_win_streak = max(max_win_streak, current_win_streak)
            elif pnl < 0:
                current_loss_streak += 1
                current_win_streak = 0
                max_loss_streak = max(max_loss_streak, current_loss_streak)
        
        return max_win_streak, max_loss_streak
    
    def _calculate_ai_accuracy(self) -> float:
        """Calculate AI decision accuracy"""
        # Simplified calculation - in production, track AI decisions vs outcomes
        trade_stats = trade_executor.get_trade_statistics()
        win_rate = trade_stats.get('win_rate', 0)
        
        # Assume AI accuracy correlates with win rate (simplified)
        return min(win_rate * 1.1, 100)  # Slight bonus for AI contribution
    
    def _estimate_signal_count(self) -> int:
        """Estimate total signals generated"""
        # Simplified estimation - in production, track actual signal counts
        return trade_executor.get_trade_statistics().get('total_trades', 0) * 3  # Assume 3 signals per trade
    
    def _estimate_avg_tokens(self) -> int:
        """Estimate average tokens per AI request"""
        # Based on our optimized prompt design
        return 750  # Estimated average (input + output)
    
    def _calculate_uptime(self) -> str:
        """Calculate system uptime"""
        # Simplified - in production, track actual uptime
        return "99.5%"  # Placeholder
    
    def _calculate_risk_of_ruin(self, win_rate: float, avg_win: float, avg_loss: float) -> float:
        """Calculate risk of ruin percentage"""
        if avg_win <= 0 or avg_loss <= 0:
            return 0.0
        
        # Simplified risk of ruin calculation
        p = win_rate / 100
        q = 1 - p
        b = avg_win / avg_loss
        
        if b <= 1:
            return 100.0  # High risk if reward/risk ratio is poor
        
        # Simplified formula
        risk_of_ruin = (q / p) ** (1 / (b - 1)) * 100
        return min(risk_of_ruin, 100.0)
    
    def _rate_cost_efficiency(self, monthly_cost: float) -> str:
        """Rate cost efficiency"""
        if monthly_cost <= 1:
            return "EXCELLENT"
        elif monthly_cost <= 3:
            return "GOOD"
        elif monthly_cost <= 5:
            return "FAIR"
        else:
            return "POOR"
    
    def _rate_risk_level(self, max_drawdown: float, sharpe_ratio: float) -> str:
        """Rate risk level"""
        if abs(max_drawdown) < 50 and sharpe_ratio > 1:
            return "LOW"
        elif abs(max_drawdown) < 100 and sharpe_ratio > 0.5:
            return "MEDIUM"
        else:
            return "HIGH"
    
    def _rate_efficiency(self, conversion_rate: float, ai_accuracy: float) -> str:
        """Rate system efficiency"""
        efficiency_score = (conversion_rate + ai_accuracy) / 2
        
        if efficiency_score >= 80:
            return "EXCELLENT"
        elif efficiency_score >= 60:
            return "GOOD"
        elif efficiency_score >= 40:
            return "FAIR"
        else:
            return "POOR"

# Global performance analytics instance
performance_analytics = PerformanceAnalytics()
