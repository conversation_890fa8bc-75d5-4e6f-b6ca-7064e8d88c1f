"""
Test Script for AI Trading Bot
"""
import sys
import time
from datetime import datetime
from config import Config
from logger import log_info, log_error, log_warning
from mt5_connector import mt5_connector
from signal_detector import signal_detector
from ai_agent import ai_agent
from trade_executor import trade_executor
from risk_manager import risk_manager
from performance_analytics import performance_analytics

def test_configuration():
    """Test configuration validation"""
    print("Testing configuration...")
    
    if Config.validate_config():
        print("✓ Configuration validation passed")
        return True
    else:
        print("✗ Configuration validation failed")
        print("Please check your .env file and ensure all required fields are set")
        return False

def test_mt5_connection():
    """Test MT5 connection"""
    print("Testing MT5 connection...")
    
    try:
        if mt5_connector.connect():
            print("✓ MT5 connection successful")
            
            # Test data retrieval
            df = mt5_connector.get_rates("EURUSD", "H1", count=100)
            if df is not None and len(df) > 0:
                print(f"✓ Data retrieval successful ({len(df)} candles)")
            else:
                print("✗ Data retrieval failed")
                return False
            
            # Test account info
            account_info = mt5_connector.get_account_info()
            if account_info:
                print(f"✓ Account info retrieved (Balance: {account_info['balance']})")
            else:
                print("✗ Account info retrieval failed")
                return False
            
            return True
        else:
            print("✗ MT5 connection failed")
            return False
            
    except Exception as e:
        print(f"✗ MT5 connection error: {e}")
        return False

def test_ai_agent():
    """Test AI agent connection"""
    print("Testing AI agent...")
    
    try:
        if ai_agent.test_connection():
            print("✓ AI agent connection successful")
            
            # Test with sample signal
            sample_signal = {
                'symbol': 'EURUSD',
                'confluence_score': 8.5,
                'direction': 'BULLISH',
                'current_price': 1.0500,
                'confluence_factors': ['BULLISH_BOS_H1', 'FRESH_DEMAND_ZONE', 'SUPPORT_LEVEL'],
                'entry_levels': [1.0500],
                'stop_loss': 1.0480,
                'take_profits': [1.0530, 1.0560]
            }
            
            decision = ai_agent.make_trading_decision(sample_signal)
            if decision:
                print(f"✓ AI decision received: {decision['decision']}")
            else:
                print("✗ AI decision failed - will use fallback")
                fallback = ai_agent.create_fallback_decision(sample_signal)
                print(f"✓ Fallback decision: {fallback['decision']}")
            
            return True
        else:
            print("✗ AI agent connection failed")
            return False
            
    except Exception as e:
        print(f"✗ AI agent error: {e}")
        return False

def test_signal_detection():
    """Test signal detection"""
    print("Testing signal detection...")
    
    try:
        # Test signal detection for EURUSD
        signal = signal_detector.detect_signals("EURUSD")
        
        if signal:
            print(f"✓ Signal detected for EURUSD (Score: {signal['confluence_score']})")
        else:
            print("✓ No signal detected (this is normal)")
        
        return True
        
    except Exception as e:
        print(f"✗ Signal detection error: {e}")
        return False

def test_risk_management():
    """Test risk management"""
    print("Testing risk management...")
    
    try:
        # Test with sample signal
        sample_signal = {
            'symbol': 'EURUSD',
            'confluence_score': 8.0,
            'direction': 'BULLISH'
        }
        
        risk_check = risk_manager.check_pre_trade_risk(sample_signal)
        
        if risk_check['approved']:
            print("✓ Risk check passed")
        else:
            print(f"✓ Risk check blocked trade: {risk_check['blocks']}")
        
        # Test risk report
        risk_report = risk_manager.get_risk_report()
        if risk_report:
            print("✓ Risk report generated")
        
        return True
        
    except Exception as e:
        print(f"✗ Risk management error: {e}")
        return False

def test_paper_trading():
    """Test paper trading execution"""
    print("Testing paper trading...")
    
    if not Config.PAPER_TRADING:
        print("⚠ Paper trading is disabled - skipping test")
        return True
    
    try:
        # Create sample signal and AI decision
        sample_signal = {
            'symbol': 'EURUSD',
            'confluence_score': 8.5,
            'direction': 'BULLISH',
            'current_price': 1.0500,
            'confluence_factors': ['TEST_SIGNAL'],
            'entry_levels': [1.0500],
            'stop_loss': 1.0480,
            'take_profits': [1.0530, 1.0560]
        }
        
        sample_decision = {
            'decision': 'TRADE',
            'confidence': 0.85,
            'entry': 1.0500,
            'sl': 1.0480,
            'tp1': 1.0530,
            'tp2': 1.0560,
            'lot_size': 0.01,
            'reasoning': 'Test trade'
        }
        
        # Execute paper trade
        result = trade_executor.execute_trade(sample_signal, sample_decision)
        
        if result:
            print(f"✓ Paper trade executed (Ticket: {result['ticket']})")
            
            # Test trade monitoring
            trade_executor.monitor_trades()
            print("✓ Trade monitoring successful")
            
            return True
        else:
            print("✗ Paper trade execution failed")
            return False
            
    except Exception as e:
        print(f"✗ Paper trading error: {e}")
        return False

def test_performance_analytics():
    """Test performance analytics"""
    print("Testing performance analytics...")
    
    try:
        report = performance_analytics.generate_comprehensive_report()
        
        if report and 'error' not in report:
            print("✓ Performance report generated")
            print(f"  Summary: {report.get('summary', {})}")
        else:
            print("✗ Performance report generation failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Performance analytics error: {e}")
        return False

def run_integration_test():
    """Run a complete integration test"""
    print("\n" + "="*50)
    print("RUNNING INTEGRATION TEST")
    print("="*50)
    
    try:
        # Test signal detection
        print("\n1. Detecting signals...")
        signal = signal_detector.detect_signals("EURUSD")
        
        if signal:
            print(f"Signal detected: {signal['symbol']} (Score: {signal['confluence_score']})")
            
            # Test risk check
            print("\n2. Checking risk...")
            risk_check = risk_manager.check_pre_trade_risk(signal)
            
            if risk_check['approved']:
                print("Risk check passed")
                
                # Test AI decision
                print("\n3. Getting AI decision...")
                ai_decision = ai_agent.make_trading_decision(signal)
                
                if not ai_decision:
                    ai_decision = ai_agent.create_fallback_decision(signal)
                    print("Using fallback decision")
                
                print(f"AI Decision: {ai_decision['decision']}")
                
                if ai_decision['decision'] == 'TRADE':
                    # Test trade execution
                    print("\n4. Executing trade...")
                    if Config.PAPER_TRADING:
                        result = trade_executor.execute_trade(signal, ai_decision)
                        if result:
                            print(f"Trade executed successfully (Ticket: {result['ticket']})")
                        else:
                            print("Trade execution failed")
                    else:
                        print("Real trading disabled - would execute trade here")
                else:
                    print("AI decided not to trade")
            else:
                print(f"Risk check blocked trade: {risk_check['blocks']}")
        else:
            print("No signals detected")
        
        print("\n✓ Integration test completed")
        return True
        
    except Exception as e:
        print(f"✗ Integration test failed: {e}")
        return False

def main():
    """Main test function"""
    print("AI Trading Bot - Test Suite")
    print("="*40)
    
    tests = [
        ("Configuration", test_configuration),
        ("MT5 Connection", test_mt5_connection),
        ("AI Agent", test_ai_agent),
        ("Signal Detection", test_signal_detection),
        ("Risk Management", test_risk_management),
        ("Paper Trading", test_paper_trading),
        ("Performance Analytics", test_performance_analytics)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                print(f"Test failed: {test_name}")
        except Exception as e:
            print(f"Test error in {test_name}: {e}")
    
    print(f"\n{'='*40}")
    print(f"Test Results: {passed}/{total} passed")
    
    if passed == total:
        print("✓ All tests passed!")
        
        # Run integration test
        if input("\nRun integration test? (y/n): ").lower() == 'y':
            run_integration_test()
    else:
        print("✗ Some tests failed. Please check the configuration and connections.")
    
    # Cleanup
    if mt5_connector.connected:
        mt5_connector.disconnect()

if __name__ == "__main__":
    main()
