"""
Risk Management System
"""
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from config import Config
from logger import log_info, log_error, log_warning, log_critical
from mt5_connector import mt5_connector
from trade_executor import trade_executor

class RiskManager:
    """Risk management and monitoring system"""
    
    def __init__(self):
        self.daily_loss_limit = Config.MAX_DAILY_LOSS
        self.max_risk_per_trade = Config.MAX_RISK_PER_TRADE
        self.max_positions = Config.MAX_POSITIONS
        self.daily_stats = {}
        self.emergency_stop = False
        self.risk_alerts = []
    
    def check_pre_trade_risk(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """Comprehensive pre-trade risk assessment"""
        try:
            risk_checks = {
                'approved': True,
                'warnings': [],
                'blocks': []
            }
            
            # 1. Emergency stop check
            if self.emergency_stop:
                risk_checks['approved'] = False
                risk_checks['blocks'].append('EMERGENCY_STOP_ACTIVE')
                return risk_checks
            
            # 2. Daily loss limit check
            daily_loss = self._calculate_daily_loss()
            if daily_loss >= self.daily_loss_limit:
                risk_checks['approved'] = False
                risk_checks['blocks'].append(f'DAILY_LOSS_LIMIT_EXCEEDED_{daily_loss:.1f}%')
            
            # 3. Maximum positions check
            active_positions = len(trade_executor.active_trades)
            if active_positions >= self.max_positions:
                risk_checks['approved'] = False
                risk_checks['blocks'].append(f'MAX_POSITIONS_REACHED_{active_positions}')
            
            # 4. Account balance check
            account_info = mt5_connector.get_account_info()
            if account_info:
                margin_level = account_info.get('margin_level', 0)
                if margin_level < 200 and margin_level > 0:  # Less than 200% margin level
                    risk_checks['approved'] = False
                    risk_checks['blocks'].append(f'LOW_MARGIN_LEVEL_{margin_level:.1f}%')
            
            # 5. Correlation risk check
            correlation_risk = self._check_correlation_risk(signal_data['symbol'])
            if correlation_risk > 0.8:
                risk_checks['warnings'].append(f'HIGH_CORRELATION_RISK_{correlation_risk:.2f}')
            
            # 6. Drawdown check
            current_drawdown = self._calculate_current_drawdown()
            if current_drawdown > 15:  # More than 15% drawdown
                risk_checks['approved'] = False
                risk_checks['blocks'].append(f'EXCESSIVE_DRAWDOWN_{current_drawdown:.1f}%')
            
            # 7. Trade frequency check
            recent_trades = self._count_recent_trades(hours=1)
            if recent_trades > 3:  # More than 3 trades in 1 hour
                risk_checks['warnings'].append(f'HIGH_TRADE_FREQUENCY_{recent_trades}')
            
            # 8. Market volatility check
            volatility_risk = self._check_market_volatility(signal_data['symbol'])
            if volatility_risk == 'HIGH':
                risk_checks['warnings'].append('HIGH_MARKET_VOLATILITY')
            
            return risk_checks
            
        except Exception as e:
            log_error(f"Error in pre-trade risk check: {e}")
            return {'approved': False, 'blocks': ['RISK_CHECK_ERROR'], 'warnings': []}
    
    def monitor_ongoing_risk(self):
        """Monitor ongoing risk across all positions"""
        try:
            # Check daily loss
            daily_loss = self._calculate_daily_loss()
            if daily_loss > self.daily_loss_limit * 0.8:  # 80% of limit
                log_warning(f"Daily loss approaching limit: {daily_loss:.1f}%")
            
            # Check account health
            account_info = mt5_connector.get_account_info()
            if account_info:
                equity = account_info['equity']
                balance = account_info['balance']
                
                # Check for significant equity drop
                equity_drop = ((balance - equity) / balance) * 100
                if equity_drop > 10:
                    log_critical(f"Significant equity drop: {equity_drop:.1f}%")
                    self._trigger_emergency_measures()
                
                # Check margin level
                margin_level = account_info.get('margin_level', 0)
                if margin_level < 150 and margin_level > 0:
                    log_critical(f"Critical margin level: {margin_level:.1f}%")
                    self._close_riskiest_positions()
            
            # Check individual position risk
            self._monitor_position_risk()
            
        except Exception as e:
            log_error(f"Error in ongoing risk monitoring: {e}")
    
    def _calculate_daily_loss(self) -> float:
        """Calculate daily loss percentage"""
        try:
            today = datetime.now().date()
            
            if today not in self.daily_stats:
                self.daily_stats[today] = {'starting_balance': 0, 'current_loss': 0}
            
            account_info = mt5_connector.get_account_info()
            if not account_info:
                return 0.0
            
            current_balance = account_info['balance']
            
            # Initialize starting balance if not set
            if self.daily_stats[today]['starting_balance'] == 0:
                self.daily_stats[today]['starting_balance'] = current_balance
            
            starting_balance = self.daily_stats[today]['starting_balance']
            loss_amount = starting_balance - current_balance
            
            if loss_amount > 0:
                loss_percentage = (loss_amount / starting_balance) * 100
                return loss_percentage
            
            return 0.0
            
        except Exception as e:
            log_error(f"Error calculating daily loss: {e}")
            return 0.0
    
    def _check_correlation_risk(self, symbol: str) -> float:
        """Check correlation risk with existing positions"""
        try:
            if not trade_executor.active_trades:
                return 0.0
            
            base_currency = symbol[:3]
            quote_currency = symbol[3:]
            
            correlation_count = 0
            total_positions = len(trade_executor.active_trades)
            
            for trade_info in trade_executor.active_trades.values():
                existing_symbol = trade_info['symbol']
                existing_base = existing_symbol[:3]
                existing_quote = existing_symbol[3:]
                
                # Check for currency overlap
                if (base_currency in [existing_base, existing_quote] or 
                    quote_currency in [existing_base, existing_quote]):
                    correlation_count += 1
            
            return correlation_count / total_positions if total_positions > 0 else 0.0
            
        except Exception as e:
            log_error(f"Error checking correlation risk: {e}")
            return 0.0
    
    def _calculate_current_drawdown(self) -> float:
        """Calculate current drawdown percentage"""
        try:
            account_info = mt5_connector.get_account_info()
            if not account_info:
                return 0.0
            
            current_equity = account_info['equity']
            
            # Get peak equity (simplified - in production, track historical peaks)
            peak_equity = account_info['balance']  # Simplified assumption
            
            if peak_equity > current_equity:
                drawdown = ((peak_equity - current_equity) / peak_equity) * 100
                return drawdown
            
            return 0.0
            
        except Exception as e:
            log_error(f"Error calculating drawdown: {e}")
            return 0.0
    
    def _count_recent_trades(self, hours: int = 1) -> int:
        """Count trades opened in recent hours"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_count = 0
            
            for trade_info in trade_executor.active_trades.values():
                if trade_info.get('open_time', datetime.min) > cutoff_time:
                    recent_count += 1
            
            # Also check recent closed trades
            for trade_record in trade_executor.trade_history[-10:]:  # Last 10 trades
                if trade_record.get('open_time', datetime.min) > cutoff_time:
                    recent_count += 1
            
            return recent_count
            
        except Exception as e:
            log_error(f"Error counting recent trades: {e}")
            return 0
    
    def _check_market_volatility(self, symbol: str) -> str:
        """Check market volatility level"""
        try:
            # Get recent price data
            df = mt5_connector.get_rates(symbol, 'H1', count=24)
            if df is None or len(df) < 20:
                return 'UNKNOWN'
            
            # Calculate ATR
            high_low = df['high'] - df['low']
            high_close = abs(df['high'] - df['close'].shift())
            low_close = abs(df['low'] - df['close'].shift())
            
            true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            atr = true_range.rolling(window=14).mean().iloc[-1]
            
            # Calculate average ATR
            avg_atr = true_range.rolling(window=14).mean().mean()
            
            # Determine volatility level
            if atr > avg_atr * 1.5:
                return 'HIGH'
            elif atr > avg_atr * 1.2:
                return 'MEDIUM'
            else:
                return 'LOW'
                
        except Exception as e:
            log_error(f"Error checking market volatility: {e}")
            return 'UNKNOWN'
    
    def _monitor_position_risk(self):
        """Monitor risk of individual positions"""
        try:
            for ticket, trade_info in trade_executor.active_trades.items():
                # Check position age
                open_time = trade_info.get('open_time', datetime.now())
                position_age = (datetime.now() - open_time).total_seconds() / 3600  # Hours
                
                if position_age > 24:  # Position open for more than 24 hours
                    log_warning(f"Long-running position: {ticket} ({position_age:.1f} hours)")
                
                # Check unrealized P&L (for real trades)
                if not Config.PAPER_TRADING:
                    self._check_position_pnl(ticket, trade_info)
                    
        except Exception as e:
            log_error(f"Error monitoring position risk: {e}")
    
    def _check_position_pnl(self, ticket: int, trade_info: Dict[str, Any]):
        """Check position P&L and take action if needed"""
        try:
            positions = mt5_connector.get_positions()
            position = next((p for p in positions if p['ticket'] == ticket), None)
            
            if position:
                unrealized_pnl = position['profit']
                account_info = mt5_connector.get_account_info()
                
                if account_info:
                    balance = account_info['balance']
                    pnl_percentage = (unrealized_pnl / balance) * 100
                    
                    # Check for excessive loss
                    if pnl_percentage < -self.max_risk_per_trade * 1.5:
                        log_critical(f"Position {ticket} exceeding risk limit: {pnl_percentage:.2f}%")
                        # Could trigger emergency close here
                        
        except Exception as e:
            log_error(f"Error checking position P&L: {e}")
    
    def _trigger_emergency_measures(self):
        """Trigger emergency risk measures"""
        try:
            log_critical("TRIGGERING EMERGENCY RISK MEASURES")
            
            # Set emergency stop
            self.emergency_stop = True
            
            # Close all positions (if configured to do so)
            if Config.PAPER_TRADING:
                log_critical("PAPER TRADING: Would close all positions")
            else:
                # In production, implement careful position closure
                log_critical("Emergency measures activated - manual intervention required")
            
        except Exception as e:
            log_error(f"Error in emergency measures: {e}")
    
    def _close_riskiest_positions(self):
        """Close the riskiest positions"""
        try:
            if not trade_executor.active_trades:
                return
            
            # Sort positions by risk (simplified)
            risky_positions = []
            
            for ticket, trade_info in trade_executor.active_trades.items():
                # Calculate risk score based on various factors
                risk_score = 0
                
                # Age factor
                open_time = trade_info.get('open_time', datetime.now())
                age_hours = (datetime.now() - open_time).total_seconds() / 3600
                risk_score += min(age_hours / 24, 1.0)  # Max 1 point for age
                
                # Confidence factor (lower confidence = higher risk)
                confidence = trade_info.get('ai_confidence', 0.5)
                risk_score += (1.0 - confidence)
                
                risky_positions.append((ticket, risk_score))
            
            # Sort by risk score (highest first)
            risky_positions.sort(key=lambda x: x[1], reverse=True)
            
            # Close the riskiest position
            if risky_positions:
                riskiest_ticket = risky_positions[0][0]
                log_warning(f"Closing riskiest position: {riskiest_ticket}")
                trade_executor._close_trade(riskiest_ticket, "RISK_MANAGEMENT")
                
        except Exception as e:
            log_error(f"Error closing risky positions: {e}")
    
    def reset_emergency_stop(self):
        """Reset emergency stop (manual intervention)"""
        self.emergency_stop = False
        log_info("Emergency stop reset")
    
    def get_risk_report(self) -> Dict[str, Any]:
        """Generate comprehensive risk report"""
        try:
            account_info = mt5_connector.get_account_info()
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'emergency_stop': self.emergency_stop,
                'daily_loss': self._calculate_daily_loss(),
                'daily_loss_limit': self.daily_loss_limit,
                'active_positions': len(trade_executor.active_trades),
                'max_positions': self.max_positions,
                'current_drawdown': self._calculate_current_drawdown(),
                'recent_trades_1h': self._count_recent_trades(1),
                'account_info': account_info,
                'risk_alerts': self.risk_alerts[-10:]  # Last 10 alerts
            }
            
            return report
            
        except Exception as e:
            log_error(f"Error generating risk report: {e}")
            return {'error': str(e)}

# Global risk manager instance
risk_manager = RiskManager()
