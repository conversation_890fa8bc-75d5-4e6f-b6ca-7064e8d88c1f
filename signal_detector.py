"""
Signal Detection and Confluence Scoring System
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
from config import Config
from logger import log_info, log_error, log_debug, log_signal
from utils import (
    calculate_distance_penalty, is_market_session, 
    is_high_impact_news_time, calculate_correlation
)
from mt5_connector import mt5_connector
from smc_analyzer import smc_analyzer

class SignalDetector:
    """Signal detection and confluence scoring system"""
    
    def __init__(self):
        self.min_confluence_score = Config.MIN_CONFLUENCE_SCORE
        self.min_confluence_factors = Config.MIN_CONFLUENCE_FACTORS
        self.active_signals = {}
        self.signal_history = []
    
    def detect_signals(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Main signal detection function"""
        try:
            log_debug(f"Detecting signals for {symbol}")
            
            # Get multi-timeframe data
            data = self._get_multi_timeframe_data(symbol)
            if not data:
                return None
            
            # Analyze each timeframe
            analysis = {}
            for tf, df in data.items():
                analysis[tf] = self._analyze_timeframe(df, tf)
            
            # Calculate confluence
            confluence_data = self._calculate_confluence(symbol, analysis)
            
            # Check if signal meets minimum requirements
            if (confluence_data['score'] >= self.min_confluence_score and 
                len(confluence_data['factors']) >= self.min_confluence_factors):
                
                # Perform risk pre-assessment
                risk_assessment = self._assess_risk(symbol, confluence_data)
                
                if risk_assessment['approved']:
                    signal = self._create_signal(symbol, confluence_data, risk_assessment)
                    log_signal(
                        symbol=symbol,
                        confluence_score=confluence_data['score'],
                        factors=confluence_data['factors'],
                        decision="SIGNAL_DETECTED"
                    )
                    return signal
                else:
                    log_debug(f"Signal rejected due to risk: {risk_assessment['reason']}")
            
            return None
            
        except Exception as e:
            log_error(f"Error detecting signals for {symbol}: {e}")
            return None
    
    def _get_multi_timeframe_data(self, symbol: str) -> Dict[str, pd.DataFrame]:
        """Get data for multiple timeframes"""
        data = {}
        
        for tf in Config.TIMEFRAMES:
            df = mt5_connector.get_rates(symbol, tf, count=200)
            if df is not None and len(df) > 50:
                data[tf] = df
            else:
                log_warning(f"Insufficient data for {symbol} {tf}")
        
        return data
    
    def _analyze_timeframe(self, df: pd.DataFrame, timeframe: str) -> Dict[str, Any]:
        """Analyze single timeframe for signals"""
        try:
            # Market structure analysis
            structure = smc_analyzer.analyze_market_structure(df, timeframe)
            
            # Order blocks
            order_blocks = smc_analyzer.detect_order_blocks(df)
            
            # Fair Value Gaps
            fvgs = smc_analyzer.detect_fair_value_gaps(df)
            
            # Supply/Demand zones
            snd_zones = smc_analyzer.detect_supply_demand_zones(df)
            
            # Support/Resistance
            sr_levels = smc_analyzer.detect_support_resistance(df)
            
            # Current price context
            current_price = df['close'].iloc[-1]
            
            return {
                'timeframe': timeframe,
                'structure': structure,
                'order_blocks': order_blocks,
                'fvgs': fvgs,
                'snd_zones': snd_zones,
                'sr_levels': sr_levels,
                'current_price': current_price,
                'atr': self._calculate_atr(df)
            }
            
        except Exception as e:
            log_error(f"Error analyzing timeframe {timeframe}: {e}")
            return {}
    
    def _calculate_confluence(self, symbol: str, analysis: Dict) -> Dict[str, Any]:
        """Calculate confluence score and factors"""
        confluence_factors = []
        total_score = 0.0
        
        try:
            current_price = analysis.get('M15', {}).get('current_price', 0)
            if not current_price:
                return {'score': 0, 'factors': [], 'direction': 'NEUTRAL'}
            
            # 1. Multi-timeframe structure alignment
            structure_score, structure_factors = self._score_structure_alignment(analysis)
            total_score += structure_score
            confluence_factors.extend(structure_factors)
            
            # 2. Order block confluence
            ob_score, ob_factors = self._score_order_block_confluence(analysis, current_price)
            total_score += ob_score
            confluence_factors.extend(ob_factors)
            
            # 3. Fair Value Gap confluence
            fvg_score, fvg_factors = self._score_fvg_confluence(analysis, current_price)
            total_score += fvg_score
            confluence_factors.extend(fvg_factors)
            
            # 4. Supply/Demand zone confluence
            snd_score, snd_factors = self._score_snd_confluence(analysis, current_price)
            total_score += snd_score
            confluence_factors.extend(snd_factors)
            
            # 5. Support/Resistance confluence
            sr_score, sr_factors = self._score_sr_confluence(analysis, current_price)
            total_score += sr_score
            confluence_factors.extend(sr_factors)
            
            # 6. Session timing bonus
            session_score, session_factors = self._score_session_timing()
            total_score += session_score
            confluence_factors.extend(session_factors)
            
            # Determine overall direction
            direction = self._determine_signal_direction(confluence_factors)
            
            return {
                'score': round(total_score, 2),
                'factors': confluence_factors,
                'direction': direction,
                'symbol': symbol
            }
            
        except Exception as e:
            log_error(f"Error calculating confluence: {e}")
            return {'score': 0, 'factors': [], 'direction': 'NEUTRAL'}
    
    def _score_structure_alignment(self, analysis: Dict) -> Tuple[float, List[str]]:
        """Score multi-timeframe structure alignment"""
        score = 0.0
        factors = []
        
        try:
            structures = {}
            for tf in ['M15', 'H1', 'H4']:
                if tf in analysis and 'structure' in analysis[tf]:
                    structures[tf] = analysis[tf]['structure']['structure']
            
            if len(structures) >= 2:
                # Check for alignment
                bullish_count = sum(1 for s in structures.values() if 'BULLISH' in s)
                bearish_count = sum(1 for s in structures.values() if 'BEARISH' in s)
                
                if bullish_count >= 2:
                    score += 2.0
                    factors.append(f"BULLISH_STRUCTURE_ALIGNMENT_{bullish_count}TF")
                elif bearish_count >= 2:
                    score += 2.0
                    factors.append(f"BEARISH_STRUCTURE_ALIGNMENT_{bearish_count}TF")
                
                # Bonus for 3TF alignment
                if bullish_count == 3 or bearish_count == 3:
                    score += 1.0
                    factors.append("3TF_STRUCTURE_ALIGNMENT")
            
        except Exception as e:
            log_error(f"Error scoring structure alignment: {e}")
        
        return score, factors

    def _score_snd_confluence(self, analysis: Dict, current_price: float) -> Tuple[float, List[str]]:
        """Score Supply/Demand zone confluence"""
        score = 0.0
        factors = []

        try:
            for tf in ['M15', 'H1', 'H4']:
                if tf not in analysis or 'snd_zones' not in analysis[tf]:
                    continue

                zones = analysis[tf]['snd_zones']
                for zone in zones[:3]:  # Check top 3 zones
                    if zone['fresh'] and not zone['tested']:
                        # Check if price is in zone
                        if zone['low'] <= current_price <= zone['high']:
                            zone_score = zone['strength'] * 0.5
                            score += zone_score
                            factors.append(f"{zone['type']}_ZONE_{tf}_STRENGTH_{zone['strength']:.1f}")

                        # Check if price is approaching zone
                        elif abs(current_price - zone['low']) / current_price < 0.001:
                            score += 0.3
                            factors.append(f"APPROACHING_{zone['type']}_ZONE_{tf}")

        except Exception as e:
            log_error(f"Error scoring S&D zones: {e}")

        return score, factors

    def _score_sr_confluence(self, analysis: Dict, current_price: float) -> Tuple[float, List[str]]:
        """Score Support/Resistance confluence"""
        score = 0.0
        factors = []

        try:
            for tf in ['H1', 'H4']:  # Focus on higher timeframes for S/R
                if tf not in analysis or 'sr_levels' not in analysis[tf]:
                    continue

                sr_data = analysis[tf]['sr_levels']

                # Check support levels
                for support in sr_data.get('support', [])[:3]:
                    distance = abs(current_price - support) / current_price
                    if distance < 0.001:  # Within 10 pips
                        penalty = calculate_distance_penalty(current_price, support)
                        level_score = 1.0 * penalty
                        score += level_score
                        factors.append(f"SUPPORT_LEVEL_{tf}")

                # Check resistance levels
                for resistance in sr_data.get('resistance', [])[:3]:
                    distance = abs(current_price - resistance) / current_price
                    if distance < 0.001:  # Within 10 pips
                        penalty = calculate_distance_penalty(current_price, resistance)
                        level_score = 1.0 * penalty
                        score += level_score
                        factors.append(f"RESISTANCE_LEVEL_{tf}")

        except Exception as e:
            log_error(f"Error scoring S/R levels: {e}")

        return score, factors

    def _score_session_timing(self) -> Tuple[float, List[str]]:
        """Score based on trading session timing"""
        score = 0.0
        factors = []

        try:
            if is_market_session("london"):
                score += 1.0
                factors.append("LONDON_SESSION")

            if is_market_session("newyork"):
                score += 1.0
                factors.append("NEW_YORK_SESSION")

            # Overlap bonus
            if is_market_session("london") and is_market_session("newyork"):
                score += 0.5
                factors.append("SESSION_OVERLAP")

        except Exception as e:
            log_error(f"Error scoring session timing: {e}")

        return score, factors

    def _determine_signal_direction(self, factors: List[str]) -> str:
        """Determine overall signal direction from factors"""
        bullish_count = sum(1 for f in factors if 'BULLISH' in f or 'DEMAND' in f or 'SUPPORT' in f)
        bearish_count = sum(1 for f in factors if 'BEARISH' in f or 'SUPPLY' in f or 'RESISTANCE' in f)

        if bullish_count > bearish_count:
            return 'BULLISH'
        elif bearish_count > bullish_count:
            return 'BEARISH'
        else:
            return 'NEUTRAL'

    def _assess_risk(self, symbol: str, confluence_data: Dict) -> Dict[str, Any]:
        """Perform risk pre-assessment"""
        try:
            # Get account info
            account_info = mt5_connector.get_account_info()
            if not account_info:
                return {'approved': False, 'reason': 'NO_ACCOUNT_INFO'}

            # Check daily loss limit
            daily_loss = self._calculate_daily_loss()
            if daily_loss >= Config.MAX_DAILY_LOSS:
                return {'approved': False, 'reason': 'DAILY_LOSS_LIMIT_EXCEEDED'}

            # Check maximum positions
            positions = mt5_connector.get_positions()
            if len(positions) >= Config.MAX_POSITIONS:
                return {'approved': False, 'reason': 'MAX_POSITIONS_REACHED'}

            # Check correlation risk
            correlation_risk = self._calculate_correlation_risk(symbol, positions)
            if correlation_risk > 0.8:
                return {'approved': False, 'reason': 'HIGH_CORRELATION_RISK'}

            # Check news proximity
            if is_high_impact_news_time():
                return {'approved': False, 'reason': 'HIGH_IMPACT_NEWS_PROXIMITY'}

            return {
                'approved': True,
                'daily_loss': daily_loss,
                'correlation_risk': correlation_risk,
                'account_balance': account_info['balance']
            }

        except Exception as e:
            log_error(f"Error in risk assessment: {e}")
            return {'approved': False, 'reason': 'RISK_ASSESSMENT_ERROR'}

    def _calculate_daily_loss(self) -> float:
        """Calculate daily loss percentage"""
        try:
            # This would track daily P&L
            # For now, return 0 as placeholder
            return 0.0
        except:
            return 0.0

    def _calculate_correlation_risk(self, symbol: str, positions: List) -> float:
        """Calculate correlation risk with existing positions"""
        try:
            if not positions:
                return 0.0

            # Simple correlation check based on currency pairs
            base_currency = symbol[:3]
            quote_currency = symbol[3:]

            correlation_count = 0
            for pos in positions:
                pos_base = pos['symbol'][:3]
                pos_quote = pos['symbol'][3:]

                if base_currency in [pos_base, pos_quote] or quote_currency in [pos_base, pos_quote]:
                    correlation_count += 1

            return correlation_count / len(positions) if positions else 0.0

        except:
            return 0.0

    def _create_signal(self, symbol: str, confluence_data: Dict, risk_data: Dict) -> Dict[str, Any]:
        """Create formatted signal for AI agent"""
        try:
            # Get current price
            price_data = mt5_connector.get_current_price(symbol)
            if not price_data:
                return None

            current_price = price_data['bid'] if confluence_data['direction'] == 'BEARISH' else price_data['ask']

            # Calculate entry levels
            entry_levels = self._calculate_entry_levels(symbol, confluence_data, current_price)

            # Calculate stop loss
            stop_loss = self._calculate_stop_loss(symbol, confluence_data, current_price)

            # Calculate take profits
            take_profits = self._calculate_take_profits(symbol, confluence_data, current_price, stop_loss)

            signal = {
                'symbol': symbol,
                'direction': confluence_data['direction'],
                'confluence_score': confluence_data['score'],
                'confluence_factors': confluence_data['factors'],
                'current_price': current_price,
                'entry_levels': entry_levels,
                'stop_loss': stop_loss,
                'take_profits': take_profits,
                'risk_assessment': risk_data,
                'timestamp': datetime.now().isoformat(),
                'spread': price_data['spread']
            }

            return signal

        except Exception as e:
            log_error(f"Error creating signal: {e}")
            return None

    def _calculate_entry_levels(self, symbol: str, confluence_data: Dict, current_price: float) -> List[float]:
        """Calculate optimal entry levels"""
        # Simple implementation - can be enhanced
        if confluence_data['direction'] == 'BULLISH':
            return [current_price, current_price - 0.0005]  # Current and slight pullback
        else:
            return [current_price, current_price + 0.0005]  # Current and slight bounce

    def _calculate_stop_loss(self, symbol: str, confluence_data: Dict, current_price: float) -> float:
        """Calculate stop loss level"""
        # Simple ATR-based stop loss
        atr = 0.0015  # Default ATR value, should be calculated from data

        if confluence_data['direction'] == 'BULLISH':
            return current_price - (atr * 1.5)
        else:
            return current_price + (atr * 1.5)

    def _calculate_take_profits(self, symbol: str, confluence_data: Dict,
                              current_price: float, stop_loss: float) -> List[float]:
        """Calculate take profit levels"""
        risk = abs(current_price - stop_loss)

        if confluence_data['direction'] == 'BULLISH':
            tp1 = current_price + (risk * 1.5)  # 1.5R
            tp2 = current_price + (risk * 2.5)  # 2.5R
        else:
            tp1 = current_price - (risk * 1.5)  # 1.5R
            tp2 = current_price - (risk * 2.5)  # 2.5R

        return [tp1, tp2]

    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> float:
        """Calculate Average True Range"""
        try:
            high_low = df['high'] - df['low']
            high_close = np.abs(df['high'] - df['close'].shift())
            low_close = np.abs(df['low'] - df['close'].shift())

            true_range = np.maximum(high_low, np.maximum(high_close, low_close))
            atr = true_range.rolling(window=period).mean().iloc[-1]

            return atr if not np.isnan(atr) else 0.0015

        except:
            return 0.0015  # Default value

# Global signal detector instance
signal_detector = SignalDetector()
    
    def _score_order_block_confluence(self, analysis: Dict, current_price: float) -> Tuple[float, List[str]]:
        """Score order block confluence"""
        score = 0.0
        factors = []
        
        try:
            for tf in ['M15', 'H1', 'H4']:
                if tf not in analysis or 'order_blocks' not in analysis[tf]:
                    continue
                
                obs = analysis[tf]['order_blocks']
                for ob in obs[:3]:  # Check top 3 OBs
                    if ob['fresh'] and not ob['tested']:
                        # Check if price is near OB
                        if (ob['low'] <= current_price <= ob['high'] or
                            abs(current_price - ob['low']) / current_price < 0.001 or
                            abs(current_price - ob['high']) / current_price < 0.001):
                            
                            ob_score = ob['strength'] * 0.3
                            score += ob_score
                            factors.append(f"{ob['type']}_OB_{tf}_STRENGTH_{ob['strength']:.1f}")
                            
                            # Fresh OB bonus
                            if ob['fresh']:
                                score += 0.5
                                factors.append(f"FRESH_{ob['type']}_OB_{tf}")
        
        except Exception as e:
            log_error(f"Error scoring order blocks: {e}")
        
        return score, factors
    
    def _score_fvg_confluence(self, analysis: Dict, current_price: float) -> Tuple[float, List[str]]:
        """Score Fair Value Gap confluence"""
        score = 0.0
        factors = []
        
        try:
            for tf in ['M15', 'H1', 'H4']:
                if tf not in analysis or 'fvgs' not in analysis[tf]:
                    continue
                
                fvgs = analysis[tf]['fvgs']
                for fvg in fvgs[:2]:  # Check top 2 FVGs
                    if not fvg['filled']:
                        # Check if price is in FVG
                        if fvg['bottom'] <= current_price <= fvg['top']:
                            fvg_score = fvg['strength'] * 0.4
                            score += fvg_score
                            factors.append(f"{fvg['type']}_FVG_{tf}_STRENGTH_{fvg['strength']:.1f}")
                        
                        # Check if price is approaching FVG
                        elif abs(current_price - fvg['bottom']) / current_price < 0.0005:
                            score += 0.5
                            factors.append(f"APPROACHING_{fvg['type']}_FVG_{tf}")
        
        except Exception as e:
            log_error(f"Error scoring FVGs: {e}")
        
        return score, factors
