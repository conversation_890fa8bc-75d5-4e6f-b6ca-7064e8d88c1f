"""
MetaTrader 5 Connection and Data Management
"""
import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from config import Config, MT5_TIMEFRAMES
from logger import log_info, log_error, log_debug, log_warning

class MT5Connector:
    """MetaTrader 5 connection and data management class"""
    
    def __init__(self):
        self.connected = False
        self.account_info = None
        self.symbols_info = {}
        
    def connect(self) -> bool:
        """Initialize MT5 connection"""
        try:
            # Initialize MT5
            if not mt5.initialize():
                log_error(f"MT5 initialization failed: {mt5.last_error()}")
                return False
            
            # Login to account
            login_result = mt5.login(
                login=Config.MT5_LOGIN,
                password=Config.MT5_PASSWORD,
                server=Config.MT5_SERVER
            )
            
            if not login_result:
                log_error(f"MT5 login failed: {mt5.last_error()}")
                mt5.shutdown()
                return False
            
            # Get account info
            self.account_info = mt5.account_info()
            if self.account_info is None:
                log_error("Failed to get account info")
                return False
            
            self.connected = True
            log_info(f"MT5 connected successfully. Account: {self.account_info.login}")
            log_info(f"Balance: {self.account_info.balance}, Equity: {self.account_info.equity}")
            
            # Initialize symbols
            self._initialize_symbols()
            
            return True
            
        except Exception as e:
            log_error(f"MT5 connection error: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from MT5"""
        if self.connected:
            mt5.shutdown()
            self.connected = False
            log_info("MT5 disconnected")
    
    def _initialize_symbols(self):
        """Initialize trading symbols information"""
        try:
            for symbol in Config.TRADING_PAIRS:
                symbol_info = mt5.symbol_info(symbol)
                if symbol_info is None:
                    log_warning(f"Symbol {symbol} not found")
                    continue
                
                # Enable symbol in Market Watch
                if not symbol_info.visible:
                    if not mt5.symbol_select(symbol, True):
                        log_warning(f"Failed to select symbol {symbol}")
                        continue
                
                self.symbols_info[symbol] = {
                    'point': symbol_info.point,
                    'digits': symbol_info.digits,
                    'spread': symbol_info.spread,
                    'volume_min': symbol_info.volume_min,
                    'volume_max': symbol_info.volume_max,
                    'volume_step': symbol_info.volume_step
                }
                
                log_debug(f"Symbol {symbol} initialized: {self.symbols_info[symbol]}")
                
        except Exception as e:
            log_error(f"Error initializing symbols: {e}")
    
    def get_rates(self, symbol: str, timeframe: str, count: int = 1000) -> Optional[pd.DataFrame]:
        """Get historical rates for a symbol"""
        try:
            if not self.connected:
                log_error("MT5 not connected")
                return None
            
            # Convert timeframe
            mt5_timeframe = MT5_TIMEFRAMES.get(timeframe.upper())
            if mt5_timeframe is None:
                log_error(f"Invalid timeframe: {timeframe}")
                return None
            
            # Get rates
            rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, count)
            
            if rates is None or len(rates) == 0:
                log_error(f"No rates data for {symbol} {timeframe}")
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame(rates)
            df['time'] = pd.to_datetime(df['time'], unit='s')
            df.set_index('time', inplace=True)
            
            log_debug(f"Retrieved {len(df)} rates for {symbol} {timeframe}")
            return df
            
        except Exception as e:
            log_error(f"Error getting rates for {symbol}: {e}")
            return None
    
    def get_current_price(self, symbol: str) -> Optional[Dict[str, float]]:
        """Get current bid/ask prices for a symbol"""
        try:
            if not self.connected:
                return None
            
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                return None
            
            return {
                'bid': tick.bid,
                'ask': tick.ask,
                'spread': tick.ask - tick.bid,
                'time': datetime.fromtimestamp(tick.time)
            }
            
        except Exception as e:
            log_error(f"Error getting current price for {symbol}: {e}")
            return None
    
    def get_account_info(self) -> Optional[Dict[str, Any]]:
        """Get current account information"""
        try:
            if not self.connected:
                return None
            
            account = mt5.account_info()
            if account is None:
                return None
            
            return {
                'balance': account.balance,
                'equity': account.equity,
                'margin': account.margin,
                'free_margin': account.margin_free,
                'margin_level': account.margin_level,
                'profit': account.profit,
                'currency': account.currency
            }
            
        except Exception as e:
            log_error(f"Error getting account info: {e}")
            return None
    
    def get_positions(self) -> List[Dict[str, Any]]:
        """Get current open positions"""
        try:
            if not self.connected:
                return []
            
            positions = mt5.positions_get()
            if positions is None:
                return []
            
            position_list = []
            for pos in positions:
                position_list.append({
                    'ticket': pos.ticket,
                    'symbol': pos.symbol,
                    'type': 'BUY' if pos.type == 0 else 'SELL',
                    'volume': pos.volume,
                    'price_open': pos.price_open,
                    'price_current': pos.price_current,
                    'sl': pos.sl,
                    'tp': pos.tp,
                    'profit': pos.profit,
                    'time': datetime.fromtimestamp(pos.time)
                })
            
            return position_list
            
        except Exception as e:
            log_error(f"Error getting positions: {e}")
            return []
    
    def check_connection(self) -> bool:
        """Check if MT5 connection is still active"""
        try:
            if not self.connected:
                return False
            
            # Try to get account info as connection test
            account = mt5.account_info()
            if account is None:
                self.connected = False
                return False
            
            return True
            
        except Exception as e:
            log_error(f"Connection check failed: {e}")
            self.connected = False
            return False
    
    def reconnect(self) -> bool:
        """Attempt to reconnect to MT5"""
        log_info("Attempting to reconnect to MT5...")
        self.disconnect()
        return self.connect()
    
    def get_symbol_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get detailed symbol information"""
        try:
            if symbol in self.symbols_info:
                return self.symbols_info[symbol]
            
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                return None
            
            return {
                'point': symbol_info.point,
                'digits': symbol_info.digits,
                'spread': symbol_info.spread,
                'volume_min': symbol_info.volume_min,
                'volume_max': symbol_info.volume_max,
                'volume_step': symbol_info.volume_step,
                'contract_size': symbol_info.trade_contract_size
            }
            
        except Exception as e:
            log_error(f"Error getting symbol info for {symbol}: {e}")
            return None

# Global MT5 connector instance
mt5_connector = MT5Connector()
