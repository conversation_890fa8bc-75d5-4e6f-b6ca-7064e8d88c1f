"""
Utility functions for the AI Trading Bot
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from config import Config, PAIR_SPECS

def calculate_pip_value(symbol: str, price: float) -> float:
    """Calculate pip value for a given symbol and price"""
    if symbol in PAIR_SPECS:
        return PAIR_SPECS[symbol]['pip_value']
    else:
        # Default to 4-digit pairs
        return 0.0001

def calculate_lot_size(account_balance: float, risk_percentage: float, 
                      entry_price: float, stop_loss: float, symbol: str) -> float:
    """Calculate optimal lot size based on risk management"""
    try:
        pip_value = calculate_pip_value(symbol, entry_price)
        risk_amount = account_balance * (risk_percentage / 100)
        
        # Calculate pips at risk
        pips_at_risk = abs(entry_price - stop_loss) / pip_value
        
        # Calculate lot size
        lot_size = risk_amount / (pips_at_risk * 10)  # 10 USD per pip for standard lot
        
        # Round to 2 decimal places and ensure minimum
        lot_size = max(0.01, round(lot_size, 2))
        
        return lot_size
    except Exception as e:
        print(f"Error calculating lot size: {e}")
        return 0.01

def is_market_session(session: str = "london") -> bool:
    """Check if current time is within specified trading session"""
    now = datetime.utcnow().time()
    
    if session.lower() == "london":
        start = datetime.strptime(Config.LONDON_OPEN, "%H:%M").time()
        end = datetime.strptime(Config.LONDON_CLOSE, "%H:%M").time()
    elif session.lower() == "newyork":
        start = datetime.strptime(Config.NEW_YORK_OPEN, "%H:%M").time()
        end = datetime.strptime(Config.NEW_YORK_CLOSE, "%H:%M").time()
    else:
        return True  # Always true for unknown sessions
    
    return start <= now <= end

def calculate_distance_penalty(current_price: float, level_price: float, 
                             max_distance_pips: int = 50) -> float:
    """Calculate penalty based on distance from key level"""
    distance_pips = abs(current_price - level_price) * 10000  # Assuming 4-digit pairs
    
    if distance_pips <= 10:
        return 1.0  # No penalty
    elif distance_pips <= max_distance_pips:
        return 1.0 - (distance_pips - 10) / (max_distance_pips - 10) * 0.5
    else:
        return 0.5  # Maximum penalty

def format_signal_for_ai(signal_data: Dict[str, Any]) -> Dict[str, Any]:
    """Format signal data for AI agent consumption (token-optimized)"""
    formatted = {
        "symbol": signal_data.get("symbol"),
        "score": round(signal_data.get("confluence_score", 0), 1),
        "factors": signal_data.get("confluence_factors", [])[:5],  # Limit factors
        "structure": {
            tf: signal_data.get("market_structure", {}).get(tf, "NEUTRAL")
            for tf in ["M15", "H1", "H4"]  # Limit timeframes
        },
        "levels": {
            "entry": signal_data.get("entry_levels", [])[:2],  # Max 2 entries
            "sl": signal_data.get("stop_loss", 0),
            "tp": signal_data.get("take_profits", [])[:2]  # Max 2 TPs
        },
        "risk": {
            "correlation": round(signal_data.get("correlation_risk", 0), 2),
            "news_minutes": signal_data.get("news_proximity_minutes", 999)
        }
    }
    return formatted

def validate_ai_response(response: Dict[str, Any]) -> bool:
    """Validate AI agent response format"""
    required_fields = ["decision", "confidence"]
    
    for field in required_fields:
        if field not in response:
            return False
    
    # Validate decision
    if response["decision"] not in ["TRADE", "NO_TRADE"]:
        return False
    
    # If decision is TRADE, validate trade parameters
    if response["decision"] == "TRADE":
        trade_fields = ["entry", "sl", "tp1", "lot_size"]
        for field in trade_fields:
            if field not in response:
                return False
    
    return True

def calculate_risk_reward_ratio(entry: float, stop_loss: float, take_profit: float) -> float:
    """Calculate risk-reward ratio"""
    try:
        risk = abs(entry - stop_loss)
        reward = abs(take_profit - entry)
        return reward / risk if risk > 0 else 0
    except:
        return 0

def is_high_impact_news_time(minutes_to_news: int = 30) -> bool:
    """Check if high-impact news is within specified minutes"""
    # This would integrate with a news calendar API
    # For now, return False as placeholder
    return False

def normalize_timeframe(tf: str) -> str:
    """Normalize timeframe string"""
    tf_map = {
        "M15": "M15", "15m": "M15", "15": "M15",
        "H1": "H1", "1h": "H1", "60": "H1",
        "H4": "H4", "4h": "H4", "240": "H4",
        "D1": "D1", "1d": "D1", "daily": "D1"
    }
    return tf_map.get(tf.upper(), tf)

def calculate_atr_stop_loss(df: pd.DataFrame, atr_multiplier: float = 2.0) -> float:
    """Calculate ATR-based stop loss"""
    try:
        # Calculate ATR (Average True Range)
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        
        true_range = np.maximum(high_low, np.maximum(high_close, low_close))
        atr = true_range.rolling(window=14).mean().iloc[-1]
        
        return atr * atr_multiplier
    except:
        return 0.002  # Default 20 pips for major pairs

def get_psychological_levels(symbol: str, current_price: float, 
                           range_pips: int = 100) -> List[float]:
    """Get psychological levels near current price"""
    levels = []
    
    # Round numbers (00, 50)
    base_price = int(current_price * 10000) / 10000
    
    for i in range(-range_pips, range_pips + 1, 50):
        level = base_price + (i * 0.0001)
        if abs(level - current_price) <= (range_pips * 0.0001):
            levels.append(round(level, 5))
    
    return sorted(levels)

def calculate_correlation(prices1: List[float], prices2: List[float]) -> float:
    """Calculate correlation between two price series"""
    try:
        if len(prices1) != len(prices2) or len(prices1) < 2:
            return 0.0
        
        correlation = np.corrcoef(prices1, prices2)[0, 1]
        return correlation if not np.isnan(correlation) else 0.0
    except:
        return 0.0

def format_price(price: float, symbol: str) -> float:
    """Format price according to symbol specifications"""
    if symbol in PAIR_SPECS:
        digits = PAIR_SPECS[symbol]['digits']
        return round(price, digits)
    return round(price, 5)  # Default to 5 digits

def time_until_next_candle(timeframe: str) -> int:
    """Calculate seconds until next candle close"""
    now = datetime.now()
    
    if timeframe == "M15":
        next_candle = now.replace(minute=(now.minute // 15 + 1) * 15, second=0, microsecond=0)
    elif timeframe == "H1":
        next_candle = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
    elif timeframe == "H4":
        next_hour = (now.hour // 4 + 1) * 4
        next_candle = now.replace(hour=next_hour % 24, minute=0, second=0, microsecond=0)
        if next_hour >= 24:
            next_candle += timedelta(days=1)
    else:  # D1
        next_candle = now.replace(hour=0, minute=0, second=0, microsecond=0) + timedelta(days=1)
    
    return int((next_candle - now).total_seconds())

def is_weekend() -> bool:
    """Check if current time is weekend (market closed)"""
    return datetime.now().weekday() >= 5  # Saturday = 5, Sunday = 6
