"""
AI Trading Bot - Main Application
"""
import time
import schedule
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import signal
import sys

from config import Config
from logger import log_info, log_error, log_critical, log_warning
from mt5_connector import mt5_connector
from signal_detector import signal_detector
from ai_agent import ai_agent
from trade_executor import trade_executor
from risk_manager import risk_manager
from utils import is_weekend, time_until_next_candle

class TradingBot:
    """Main trading bot application"""
    
    def __init__(self):
        self.running = False
        self.last_signal_check = {}
        self.performance_stats = {}
        self.startup_time = datetime.now()
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def start(self):
        """Start the trading bot"""
        try:
            log_info("=" * 60)
            log_info("AI TRADING BOT STARTING")
            log_info("=" * 60)
            
            # Validate configuration
            if not Config.validate_config():
                log_critical("Configuration validation failed")
                return False
            
            # Connect to MT5
            if not mt5_connector.connect():
                log_critical("Failed to connect to MT5")
                return False
            
            # Test AI connection
            if not ai_agent.test_connection():
                log_warning("AI agent connection test failed - will use fallback decisions")
            
            # Initialize components
            self._initialize_components()
            
            # Setup scheduled tasks
            self._setup_schedule()
            
            # Start main loop
            self.running = True
            log_info("Trading bot started successfully")
            log_info(f"Paper Trading Mode: {Config.PAPER_TRADING}")
            log_info(f"Trading Pairs: {Config.TRADING_PAIRS}")
            log_info(f"Min Confluence Score: {Config.MIN_CONFLUENCE_SCORE}")
            
            self._main_loop()
            
        except Exception as e:
            log_critical(f"Critical error in bot startup: {e}")
            return False
    
    def stop(self):
        """Stop the trading bot"""
        log_info("Stopping trading bot...")
        self.running = False
        
        # Disconnect from MT5
        mt5_connector.disconnect()
        
        # Generate final report
        self._generate_final_report()
        
        log_info("Trading bot stopped")
    
    def _initialize_components(self):
        """Initialize all bot components"""
        try:
            # Reset daily stats if new day
            self._check_new_day()
            
            # Initialize last signal check times
            for symbol in Config.TRADING_PAIRS:
                self.last_signal_check[symbol] = datetime.min
            
            log_info("Components initialized successfully")
            
        except Exception as e:
            log_error(f"Error initializing components: {e}")
    
    def _setup_schedule(self):
        """Setup scheduled tasks"""
        try:
            # Signal detection every 15 minutes
            schedule.every(15).minutes.do(self._run_signal_detection)
            
            # Trade monitoring every 5 minutes
            schedule.every(5).minutes.do(self._monitor_trades)
            
            # Risk monitoring every 10 minutes
            schedule.every(10).minutes.do(self._monitor_risk)
            
            # Performance update every hour
            schedule.every().hour.do(self._update_performance)
            
            # Daily cleanup at midnight
            schedule.every().day.at("00:01").do(self._daily_cleanup)
            
            log_info("Scheduled tasks configured")
            
        except Exception as e:
            log_error(f"Error setting up schedule: {e}")
    
    def _main_loop(self):
        """Main bot execution loop"""
        try:
            while self.running:
                # Check if market is open
                if is_weekend():
                    log_info("Market closed (weekend) - sleeping")
                    time.sleep(3600)  # Sleep for 1 hour
                    continue
                
                # Run scheduled tasks
                schedule.run_pending()
                
                # Sleep for a short interval
                time.sleep(30)  # 30 seconds
                
        except KeyboardInterrupt:
            log_info("Received keyboard interrupt")
        except Exception as e:
            log_critical(f"Critical error in main loop: {e}")
        finally:
            self.stop()
    
    def _run_signal_detection(self):
        """Run signal detection for all symbols"""
        try:
            log_debug("Running signal detection...")
            
            for symbol in Config.TRADING_PAIRS:
                try:
                    # Check if enough time has passed since last check
                    time_since_last = datetime.now() - self.last_signal_check[symbol]
                    if time_since_last.total_seconds() < 900:  # 15 minutes
                        continue
                    
                    # Detect signals
                    signal_data = signal_detector.detect_signals(symbol)
                    
                    if signal_data:
                        log_info(f"Signal detected for {symbol}: Score {signal_data['confluence_score']}")
                        
                        # Pre-trade risk check
                        risk_check = risk_manager.check_pre_trade_risk(signal_data)
                        
                        if risk_check['approved']:
                            # Get AI decision
                            ai_decision = ai_agent.make_trading_decision(signal_data)
                            
                            if not ai_decision:
                                # Use fallback decision
                                ai_decision = ai_agent.create_fallback_decision(signal_data)
                            
                            if ai_decision and ai_decision['decision'] == 'TRADE':
                                # Execute trade
                                trade_result = trade_executor.execute_trade(signal_data, ai_decision)
                                
                                if trade_result:
                                    log_info(f"Trade executed for {symbol}: {trade_result['ticket']}")
                                else:
                                    log_error(f"Failed to execute trade for {symbol}")
                        else:
                            log_warning(f"Trade blocked for {symbol}: {risk_check['blocks']}")
                    
                    # Update last check time
                    self.last_signal_check[symbol] = datetime.now()
                    
                except Exception as e:
                    log_error(f"Error processing {symbol}: {e}")
                    
        except Exception as e:
            log_error(f"Error in signal detection: {e}")
    
    def _monitor_trades(self):
        """Monitor active trades"""
        try:
            trade_executor.monitor_trades()
            
        except Exception as e:
            log_error(f"Error monitoring trades: {e}")
    
    def _monitor_risk(self):
        """Monitor risk across all positions"""
        try:
            risk_manager.monitor_ongoing_risk()
            
        except Exception as e:
            log_error(f"Error in risk monitoring: {e}")
    
    def _update_performance(self):
        """Update performance statistics"""
        try:
            # Get trading statistics
            trade_stats = trade_executor.get_trade_statistics()
            
            # Get AI usage statistics
            ai_stats = ai_agent.get_usage_stats()
            
            # Get account info
            account_info = mt5_connector.get_account_info()
            
            # Update performance stats
            self.performance_stats = {
                'timestamp': datetime.now().isoformat(),
                'uptime_hours': (datetime.now() - self.startup_time).total_seconds() / 3600,
                'trade_stats': trade_stats,
                'ai_stats': ai_stats,
                'account_info': account_info,
                'active_positions': len(trade_executor.active_trades)
            }
            
            # Log key metrics
            if trade_stats.get('total_trades', 0) > 0:
                log_info(f"Performance Update - Trades: {trade_stats['total_trades']}, "
                        f"Win Rate: {trade_stats['win_rate']}%, "
                        f"Total Pips: {trade_stats['total_pips']}")
            
            if ai_stats.get('total_requests', 0) > 0:
                log_info(f"AI Usage - Requests: {ai_stats['total_requests']}, "
                        f"Total Cost: ${ai_stats['total_cost']:.4f}")
                        
        except Exception as e:
            log_error(f"Error updating performance: {e}")
    
    def _daily_cleanup(self):
        """Daily cleanup and maintenance"""
        try:
            log_info("Running daily cleanup...")
            
            # Reset AI usage stats if needed
            # ai_agent.reset_usage_stats()
            
            # Clean old trade history (keep last 100 trades)
            if len(trade_executor.trade_history) > 100:
                trade_executor.trade_history = trade_executor.trade_history[-100:]
            
            # Generate daily report
            self._generate_daily_report()
            
            log_info("Daily cleanup completed")
            
        except Exception as e:
            log_error(f"Error in daily cleanup: {e}")
    
    def _check_new_day(self):
        """Check if it's a new trading day"""
        try:
            current_date = datetime.now().date()
            
            # Reset daily stats for new day
            if current_date not in risk_manager.daily_stats:
                log_info(f"New trading day: {current_date}")
                # Daily stats will be initialized when first accessed
                
        except Exception as e:
            log_error(f"Error checking new day: {e}")
    
    def _generate_daily_report(self):
        """Generate daily performance report"""
        try:
            report = {
                'date': datetime.now().date().isoformat(),
                'trade_stats': trade_executor.get_trade_statistics(),
                'ai_stats': ai_agent.get_usage_stats(),
                'risk_report': risk_manager.get_risk_report(),
                'performance_stats': self.performance_stats
            }
            
            log_info("=== DAILY REPORT ===")
            log_info(f"Date: {report['date']}")
            
            if report['trade_stats'].get('total_trades', 0) > 0:
                log_info(f"Trades: {report['trade_stats']['total_trades']}")
                log_info(f"Win Rate: {report['trade_stats']['win_rate']}%")
                log_info(f"Total Pips: {report['trade_stats']['total_pips']}")
            
            log_info(f"AI Requests: {report['ai_stats'].get('total_requests', 0)}")
            log_info(f"AI Cost: ${report['ai_stats'].get('total_cost', 0):.4f}")
            log_info("==================")
            
        except Exception as e:
            log_error(f"Error generating daily report: {e}")
    
    def _generate_final_report(self):
        """Generate final report on shutdown"""
        try:
            uptime = datetime.now() - self.startup_time
            
            log_info("=== FINAL REPORT ===")
            log_info(f"Total Uptime: {uptime}")
            log_info(f"Total Trades: {trade_executor.get_trade_statistics().get('total_trades', 0)}")
            log_info(f"Total AI Cost: ${ai_agent.get_usage_stats().get('total_cost', 0):.4f}")
            log_info("===================")
            
        except Exception as e:
            log_error(f"Error generating final report: {e}")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        log_info(f"Received signal {signum}")
        self.running = False

def main():
    """Main entry point"""
    try:
        # Create and start the trading bot
        bot = TradingBot()
        bot.start()
        
    except Exception as e:
        log_critical(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
