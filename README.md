# AI Trading Bot

A sophisticated automated trading system that combines Smart Money Concepts (SMC), Supply & Demand analysis, and AI-powered decision making for forex trading.

## 🚀 Features

- **Smart Money Concepts Analysis**: BOS/CHoCH detection, Order Blocks, Fair Value Gaps
- **Supply & Demand Zones**: Rally-Base-Drop and Drop-Base-Rally pattern detection
- **Multi-Timeframe Analysis**: M15, H1, H4, D1 confluence scoring
- **AI-Powered Decisions**: DeepSeek integration for final trading decisions
- **Risk Management**: Comprehensive risk controls and monitoring
- **Paper Trading**: Safe testing mode without real money
- **Performance Analytics**: Detailed reporting and optimization insights

## 📋 Requirements

- Python 3.8+
- MetaTrader 5 terminal
- DeepSeek API key
- Valid MT5 trading account

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ai-trading-bot
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure environment variables**
   Copy `.env` file and update with your credentials:
   ```bash
   # MetaTrader 5 Configuration
   MT5_LOGIN=your_mt5_login
   MT5_PASSWORD=your_mt5_password
   MT5_SERVER=your_mt5_server
   
   # AI Agent Configuration
   DEEPSEEK_API_KEY=your_deepseek_api_key
   
   # Trading Configuration
   TRADING_PAIRS=EURUSD,GBPUSD,USDJPY
   PAPER_TRADING=True
   ```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `MT5_LOGIN` | MetaTrader 5 login number | Required |
| `MT5_PASSWORD` | MetaTrader 5 password | Required |
| `MT5_SERVER` | MetaTrader 5 server name | Required |
| `DEEPSEEK_API_KEY` | DeepSeek API key | Required |
| `TRADING_PAIRS` | Comma-separated currency pairs | EURUSD,GBPUSD |
| `PAPER_TRADING` | Enable paper trading mode | True |
| `MAX_RISK_PER_TRADE` | Maximum risk per trade (%) | 2.0 |
| `MAX_DAILY_LOSS` | Maximum daily loss (%) | 5.0 |
| `MIN_CONFLUENCE_SCORE` | Minimum confluence score | 7.5 |

### Risk Management Settings

- **Position Sizing**: Automatic calculation based on account balance and risk percentage
- **Stop Loss**: ATR-based or structure-based stop losses
- **Take Profit**: Multiple TP levels with partial closing
- **Daily Loss Limits**: Automatic trading halt when limits reached
- **Correlation Risk**: Prevents over-exposure to correlated pairs

## 🚀 Usage

### Running the Bot

1. **Test the system first**
   ```bash
   python test_bot.py
   ```

2. **Start the trading bot**
   ```bash
   python main.py
   ```

### Paper Trading Mode

For safe testing, ensure `PAPER_TRADING=True` in your `.env` file. This mode:
- Simulates all trades without real money
- Tracks hypothetical performance
- Logs all decisions and outcomes
- Allows safe system testing

### Real Trading Mode

⚠️ **WARNING**: Only use real trading mode after thorough testing!

Set `PAPER_TRADING=False` and ensure:
- Sufficient account balance
- Proper risk management settings
- Continuous monitoring capability

## 📊 System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   MT5 Data      │───▶│  SMC Analysis    │───▶│ Signal Detection│
│   Pipeline      │    │  Engine          │    │ & Confluence    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐    ┌──────────────────┐             │
│ Trade Execution │◀───│   AI Agent       │◀────────────┘
│ & Monitoring    │    │  (DeepSeek)      │
└─────────────────┘    └──────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌──────────────────┐
│ Risk Management │    │ Performance      │
│ & Monitoring    │    │ Analytics        │
└─────────────────┘    └──────────────────┘
```

## 🔍 Signal Detection Process

1. **Multi-Timeframe Data Collection**: Gather OHLCV data from M15, H1, H4, D1
2. **SMC Analysis**: Detect market structure, order blocks, FVGs
3. **S&D Zone Detection**: Identify supply/demand zones
4. **Confluence Scoring**: Calculate weighted confluence score
5. **Risk Pre-Assessment**: Check account limits and correlation
6. **AI Decision**: Send high-confluence signals to AI agent
7. **Trade Execution**: Execute approved trades with proper risk management

## 📈 Performance Monitoring

The bot provides comprehensive performance analytics:

- **Trading Metrics**: Win rate, profit factor, average R:R
- **Risk Metrics**: Maximum drawdown, Sharpe ratio, risk of ruin
- **Cost Efficiency**: AI usage costs, cost per trade
- **System Efficiency**: Signal conversion rates, uptime

Access reports through:
```python
from performance_analytics import performance_analytics
report = performance_analytics.generate_comprehensive_report()
```

## 🛡️ Risk Management Features

### Pre-Trade Risk Checks
- Daily loss limit monitoring
- Maximum position limits
- Correlation risk assessment
- Account margin level checks
- News proximity filtering

### Ongoing Risk Monitoring
- Real-time P&L tracking
- Structure invalidation detection
- Emergency stop mechanisms
- Automatic position closure

### Risk Controls
- Position sizing based on account percentage
- ATR-based stop losses
- Partial profit taking at multiple levels
- Trailing stops to breakeven

## 🔧 Customization

### Adding New Currency Pairs
Update `TRADING_PAIRS` in `.env`:
```
TRADING_PAIRS=EURUSD,GBPUSD,USDJPY,AUDUSD,USDCAD
```

### Adjusting Confluence Factors
Modify weights in `signal_detector.py`:
```python
def _score_structure_alignment(self, analysis):
    # Adjust scoring weights here
    score += 2.0  # Structure alignment weight
```

### Custom Risk Rules
Add custom risk checks in `risk_manager.py`:
```python
def check_custom_risk(self, signal_data):
    # Add your custom risk logic here
    pass
```

## 📝 Logging

The bot maintains comprehensive logs:
- **Trading decisions**: All signals and AI responses
- **Trade execution**: Entry/exit details and P&L
- **Risk events**: Limit breaches and emergency actions
- **Performance metrics**: Regular performance updates

Logs are saved to `trading_bot.log` with configurable levels.

## 🚨 Important Notes

### Cost Management
- AI costs are optimized for <$0.05/month target
- Token usage is minimized through prompt optimization
- Only high-confluence signals (≥7.5) trigger AI calls

### Market Hours
- Bot respects forex market hours
- Enhanced activity during London/NY sessions
- Automatic pause during weekends

### Safety Features
- Emergency stop mechanisms
- Automatic risk limit enforcement
- Structure invalidation detection
- News proximity filtering

## 🐛 Troubleshooting

### Common Issues

1. **MT5 Connection Failed**
   - Check login credentials in `.env`
   - Ensure MT5 terminal is running
   - Verify server name is correct

2. **AI API Errors**
   - Verify DeepSeek API key
   - Check internet connection
   - Monitor API rate limits

3. **No Signals Detected**
   - Lower `MIN_CONFLUENCE_SCORE` for testing
   - Check if market is active
   - Verify data feed is working

### Debug Mode
Enable debug logging:
```
LOG_LEVEL=DEBUG
```

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review log files for error details
3. Test individual components using `test_bot.py`

## ⚖️ Disclaimer

This trading bot is for educational and research purposes. Trading forex involves substantial risk of loss. Past performance does not guarantee future results. Use at your own risk and never trade with money you cannot afford to lose.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
