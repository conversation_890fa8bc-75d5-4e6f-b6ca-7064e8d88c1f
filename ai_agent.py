"""
AI Agent Integration for Trading Decisions
"""
import json
import requests
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from config import Config
from logger import log_info, log_error, log_debug, log_ai_request
from utils import format_signal_for_ai, validate_ai_response

class AIAgent:
    """DeepSeek AI Agent for trading decisions"""
    
    def __init__(self):
        self.api_key = Config.DEEPSEEK_API_KEY
        self.api_url = Config.DEEPSEEK_API_URL
        self.model = Config.DEEPSEEK_MODEL
        self.max_tokens = Config.DEEPSEEK_MAX_TOKENS
        self.request_count = 0
        self.total_cost = 0.0
        self.rate_limit_delay = 1.0  # Seconds between requests
        self.last_request_time = 0
    
    def make_trading_decision(self, signal_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Make trading decision using AI agent"""
        try:
            # Rate limiting
            self._enforce_rate_limit()
            
            # Format signal for AI (token-optimized)
            formatted_signal = format_signal_for_ai(signal_data)
            
            # Create optimized prompt
            prompt = self._create_optimized_prompt(formatted_signal)
            
            # Make API request
            response = self._make_api_request(prompt)
            
            if response:
                # Parse and validate response
                decision = self._parse_ai_response(response)
                
                if decision and validate_ai_response(decision):
                    # Log the request
                    self._log_ai_request(signal_data['symbol'], prompt, response, decision)
                    return decision
                else:
                    log_error("Invalid AI response format")
                    return None
            
            return None
            
        except Exception as e:
            log_error(f"Error in AI trading decision: {e}")
            return None
    
    def _create_optimized_prompt(self, signal_data: Dict[str, Any]) -> str:
        """Create token-optimized prompt for AI agent"""
        try:
            # Ultra-concise prompt to minimize tokens
            prompt = f"""Trading Signal Analysis:
Symbol: {signal_data['symbol']}
Score: {signal_data['score']}/10
Factors: {', '.join(signal_data['factors'][:5])}
Structure: {signal_data['structure']}
Entry: {signal_data['levels']['entry']}
SL: {signal_data['levels']['sl']}
TP: {signal_data['levels']['tp']}
Risk: Corr={signal_data['risk']['correlation']}, News={signal_data['risk']['news_minutes']}min

Respond JSON only:
{{"decision": "TRADE/NO_TRADE", "confidence": 0.0-1.0, "entry": price, "sl": price, "tp1": price, "tp2": price, "lot_size": 0.01-1.0, "reasoning": "brief reason"}}

Decision:"""
            
            return prompt
            
        except Exception as e:
            log_error(f"Error creating prompt: {e}")
            return ""
    
    def _make_api_request(self, prompt: str) -> Optional[Dict[str, Any]]:
        """Make API request to DeepSeek"""
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }
            
            payload = {
                'model': self.model,
                'messages': [
                    {
                        'role': 'system',
                        'content': 'You are an expert forex trader. Analyze signals and make precise trading decisions. Respond only with valid JSON.'
                    },
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ],
                'max_tokens': self.max_tokens,
                'temperature': 0.1,  # Low temperature for consistent decisions
                'top_p': 0.9
            }
            
            # Make request with timeout
            response = requests.post(
                self.api_url,
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                self.request_count += 1
                return response.json()
            else:
                log_error(f"API request failed: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            log_error("API request timeout")
            return None
        except requests.exceptions.RequestException as e:
            log_error(f"API request error: {e}")
            return None
        except Exception as e:
            log_error(f"Unexpected error in API request: {e}")
            return None
    
    def _parse_ai_response(self, response: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Parse AI response and extract decision"""
        try:
            if 'choices' not in response or not response['choices']:
                log_error("No choices in AI response")
                return None
            
            content = response['choices'][0]['message']['content'].strip()
            
            # Try to extract JSON from response
            json_start = content.find('{')
            json_end = content.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_content = content[json_start:json_end]
                decision = json.loads(json_content)
                
                # Validate required fields
                required_fields = ['decision', 'confidence']
                for field in required_fields:
                    if field not in decision:
                        log_error(f"Missing required field: {field}")
                        return None
                
                # Normalize decision
                decision['decision'] = decision['decision'].upper()
                
                # Validate decision value
                if decision['decision'] not in ['TRADE', 'NO_TRADE']:
                    log_error(f"Invalid decision value: {decision['decision']}")
                    return None
                
                # Ensure confidence is float
                decision['confidence'] = float(decision['confidence'])
                
                return decision
            else:
                log_error("No valid JSON found in AI response")
                return None
                
        except json.JSONDecodeError as e:
            log_error(f"JSON decode error: {e}")
            return None
        except Exception as e:
            log_error(f"Error parsing AI response: {e}")
            return None
    
    def _enforce_rate_limit(self):
        """Enforce rate limiting between API requests"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _log_ai_request(self, symbol: str, prompt: str, response: Dict, decision: Dict):
        """Log AI request details for cost tracking"""
        try:
            # Estimate token usage (rough approximation)
            input_tokens = len(prompt.split()) * 1.3  # Rough token estimation
            output_tokens = len(str(response.get('choices', [{}])[0].get('message', {}).get('content', '')).split()) * 1.3
            
            # Estimate cost (DeepSeek pricing - adjust as needed)
            input_cost = input_tokens * 0.00014 / 1000  # $0.14 per 1K input tokens
            output_cost = output_tokens * 0.00028 / 1000  # $0.28 per 1K output tokens
            total_cost = input_cost + output_cost
            
            self.total_cost += total_cost
            
            log_ai_request(
                symbol=symbol,
                input_tokens=int(input_tokens),
                output_tokens=int(output_tokens),
                cost=total_cost,
                decision=decision['decision']
            )
            
            log_debug(f"AI Request - Tokens: {int(input_tokens)}+{int(output_tokens)}, Cost: ${total_cost:.6f}")
            
        except Exception as e:
            log_error(f"Error logging AI request: {e}")
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get AI usage statistics"""
        return {
            'total_requests': self.request_count,
            'total_cost': round(self.total_cost, 6),
            'average_cost_per_request': round(self.total_cost / max(1, self.request_count), 6),
            'estimated_monthly_cost': round(self.total_cost * 30, 2) if self.request_count > 0 else 0
        }
    
    def reset_usage_stats(self):
        """Reset usage statistics"""
        self.request_count = 0
        self.total_cost = 0.0
        log_info("AI usage statistics reset")
    
    def test_connection(self) -> bool:
        """Test AI API connection"""
        try:
            test_prompt = "Test connection. Respond with: {'status': 'ok'}"
            response = self._make_api_request(test_prompt)
            
            if response and 'choices' in response:
                log_info("AI API connection test successful")
                return True
            else:
                log_error("AI API connection test failed")
                return False
                
        except Exception as e:
            log_error(f"AI API connection test error: {e}")
            return False
    
    def create_fallback_decision(self, signal_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback decision when AI is unavailable"""
        try:
            # Simple rule-based fallback
            score = signal_data.get('confluence_score', 0)
            direction = signal_data.get('direction', 'NEUTRAL')
            
            if score >= 8.0 and direction != 'NEUTRAL':
                decision = {
                    'decision': 'TRADE',
                    'confidence': min(0.8, score / 10),
                    'entry': signal_data.get('current_price', 0),
                    'sl': signal_data.get('stop_loss', 0),
                    'tp1': signal_data.get('take_profits', [0])[0] if signal_data.get('take_profits') else 0,
                    'tp2': signal_data.get('take_profits', [0, 0])[1] if len(signal_data.get('take_profits', [])) > 1 else 0,
                    'lot_size': 0.01,
                    'reasoning': 'Fallback decision - high confluence score'
                }
            else:
                decision = {
                    'decision': 'NO_TRADE',
                    'confidence': 0.5,
                    'reasoning': 'Fallback decision - insufficient confluence'
                }
            
            log_info(f"Created fallback decision: {decision['decision']}")
            return decision
            
        except Exception as e:
            log_error(f"Error creating fallback decision: {e}")
            return {
                'decision': 'NO_TRADE',
                'confidence': 0.0,
                'reasoning': 'Error in fallback decision'
            }

# Global AI agent instance
ai_agent = AIAgent()
