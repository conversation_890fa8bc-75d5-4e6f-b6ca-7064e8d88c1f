"""
Trade Execution and Order Management
"""
import MetaTrader5 as mt5
import time
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from config import Config
from logger import log_info, log_error, log_debug, log_trade
from utils import calculate_lot_size, format_price
from mt5_connector import mt5_connector

class TradeExecutor:
    """Trade execution and order management class"""
    
    def __init__(self):
        self.active_trades = {}
        self.trade_history = []
        self.paper_trading = Config.PAPER_TRADING
        self.magic_number = 12345  # Unique identifier for bot trades
    
    def execute_trade(self, signal_data: Dict[str, Any], ai_decision: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Execute trade based on AI decision"""
        try:
            if ai_decision['decision'] != 'TRADE':
                log_info(f"AI decided NO_TRADE for {signal_data['symbol']}")
                return None
            
            symbol = signal_data['symbol']
            
            # Validate trade parameters
            if not self._validate_trade_parameters(ai_decision):
                log_error("Invalid trade parameters")
                return None
            
            # Calculate lot size
            account_info = mt5_connector.get_account_info()
            if not account_info:
                log_error("Cannot get account info for lot size calculation")
                return None
            
            lot_size = calculate_lot_size(
                account_balance=account_info['balance'],
                risk_percentage=Config.MAX_RISK_PER_TRADE,
                entry_price=ai_decision['entry'],
                stop_loss=ai_decision['sl'],
                symbol=symbol
            )
            
            # Override with AI suggested lot size if reasonable
            if 0.01 <= ai_decision.get('lot_size', 0) <= 1.0:
                lot_size = min(lot_size, ai_decision['lot_size'])
            
            # Determine order type
            current_price = signal_data['current_price']
            entry_price = ai_decision['entry']
            
            if signal_data['direction'] == 'BULLISH':
                order_type = mt5.ORDER_TYPE_BUY if entry_price <= current_price + 0.0005 else mt5.ORDER_TYPE_BUY_LIMIT
            else:
                order_type = mt5.ORDER_TYPE_SELL if entry_price >= current_price - 0.0005 else mt5.ORDER_TYPE_SELL_LIMIT
            
            # Execute trade
            if self.paper_trading:
                result = self._execute_paper_trade(symbol, order_type, lot_size, ai_decision)
            else:
                result = self._execute_real_trade(symbol, order_type, lot_size, ai_decision)
            
            if result:
                # Log trade
                log_trade(
                    action="OPEN",
                    symbol=symbol,
                    lot_size=lot_size,
                    price=entry_price,
                    sl=ai_decision['sl'],
                    tp=ai_decision.get('tp1', 0),
                    confidence=ai_decision['confidence'],
                    reasoning=ai_decision.get('reasoning', '')
                )
                
                # Store trade info
                self.active_trades[result['ticket']] = {
                    'symbol': symbol,
                    'type': 'BUY' if 'BUY' in str(order_type) else 'SELL',
                    'lot_size': lot_size,
                    'entry_price': entry_price,
                    'sl': ai_decision['sl'],
                    'tp1': ai_decision.get('tp1'),
                    'tp2': ai_decision.get('tp2'),
                    'open_time': datetime.now(),
                    'ai_confidence': ai_decision['confidence'],
                    'signal_score': signal_data['confluence_score']
                }
            
            return result
            
        except Exception as e:
            log_error(f"Error executing trade: {e}")
            return None
    
    def _execute_real_trade(self, symbol: str, order_type: int, lot_size: float, 
                           ai_decision: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Execute real trade on MT5"""
        try:
            # Prepare order request
            request = {
                'action': mt5.TRADE_ACTION_DEAL,
                'symbol': symbol,
                'volume': lot_size,
                'type': order_type,
                'price': ai_decision['entry'],
                'sl': ai_decision['sl'],
                'tp': ai_decision.get('tp1', 0),
                'deviation': 20,
                'magic': self.magic_number,
                'comment': f"AI_Bot_{ai_decision['confidence']:.2f}",
                'type_time': mt5.ORDER_TIME_GTC,
                'type_filling': mt5.ORDER_FILLING_IOC,
            }
            
            # Send order
            result = mt5.order_send(request)
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                log_error(f"Order failed: {result.retcode} - {result.comment}")
                return None
            
            log_info(f"Trade executed successfully: {result.order}")
            
            return {
                'ticket': result.order,
                'symbol': symbol,
                'volume': lot_size,
                'price': result.price,
                'type': order_type,
                'sl': ai_decision['sl'],
                'tp': ai_decision.get('tp1', 0)
            }
            
        except Exception as e:
            log_error(f"Error in real trade execution: {e}")
            return None
    
    def _execute_paper_trade(self, symbol: str, order_type: int, lot_size: float, 
                            ai_decision: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Execute paper trade (simulation)"""
        try:
            # Generate fake ticket number
            ticket = int(time.time() * 1000) % 1000000
            
            log_info(f"PAPER TRADE: {symbol} {lot_size} lots at {ai_decision['entry']}")
            
            return {
                'ticket': ticket,
                'symbol': symbol,
                'volume': lot_size,
                'price': ai_decision['entry'],
                'type': order_type,
                'sl': ai_decision['sl'],
                'tp': ai_decision.get('tp1', 0),
                'paper_trade': True
            }
            
        except Exception as e:
            log_error(f"Error in paper trade execution: {e}")
            return None
    
    def monitor_trades(self):
        """Monitor active trades and manage them"""
        try:
            if not self.active_trades:
                return
            
            for ticket, trade_info in list(self.active_trades.items()):
                if self.paper_trading:
                    self._monitor_paper_trade(ticket, trade_info)
                else:
                    self._monitor_real_trade(ticket, trade_info)
                    
        except Exception as e:
            log_error(f"Error monitoring trades: {e}")
    
    def _monitor_real_trade(self, ticket: int, trade_info: Dict[str, Any]):
        """Monitor real trade"""
        try:
            # Get current position
            positions = mt5.positions_get(ticket=ticket)
            
            if not positions:
                # Trade is closed
                self._handle_trade_closure(ticket, trade_info)
                return
            
            position = positions[0]
            current_price = position.price_current
            
            # Check for partial close at TP1
            if (trade_info.get('tp2') and 
                not trade_info.get('tp1_hit') and
                self._check_tp_hit(current_price, trade_info['tp1'], trade_info['type'])):
                
                self._partial_close_trade(ticket, 0.5)  # Close 50% at TP1
                trade_info['tp1_hit'] = True
                
                # Move SL to breakeven
                self._modify_stop_loss(ticket, trade_info['entry_price'])
            
            # Check for structure invalidation
            if self._check_structure_invalidation(trade_info):
                self._close_trade(ticket, "STRUCTURE_INVALIDATION")
            
        except Exception as e:
            log_error(f"Error monitoring real trade {ticket}: {e}")
    
    def _monitor_paper_trade(self, ticket: int, trade_info: Dict[str, Any]):
        """Monitor paper trade"""
        try:
            # Get current price
            price_data = mt5_connector.get_current_price(trade_info['symbol'])
            if not price_data:
                return
            
            current_price = price_data['bid'] if trade_info['type'] == 'SELL' else price_data['ask']
            
            # Check SL hit
            if self._check_sl_hit(current_price, trade_info['sl'], trade_info['type']):
                self._close_paper_trade(ticket, current_price, "SL_HIT")
                return
            
            # Check TP hit
            if self._check_tp_hit(current_price, trade_info['tp1'], trade_info['type']):
                self._close_paper_trade(ticket, current_price, "TP1_HIT")
                return
            
        except Exception as e:
            log_error(f"Error monitoring paper trade {ticket}: {e}")
    
    def _check_sl_hit(self, current_price: float, sl: float, trade_type: str) -> bool:
        """Check if stop loss is hit"""
        if trade_type == 'BUY':
            return current_price <= sl
        else:
            return current_price >= sl
    
    def _check_tp_hit(self, current_price: float, tp: float, trade_type: str) -> bool:
        """Check if take profit is hit"""
        if trade_type == 'BUY':
            return current_price >= tp
        else:
            return current_price <= tp
    
    def _check_structure_invalidation(self, trade_info: Dict[str, Any]) -> bool:
        """Check if market structure has been invalidated"""
        # Simplified structure invalidation check
        # In a full implementation, this would re-analyze the market structure
        return False
    
    def _partial_close_trade(self, ticket: int, percentage: float):
        """Partially close a trade"""
        try:
            if self.paper_trading:
                log_info(f"PAPER TRADE: Partial close {percentage*100}% of trade {ticket}")
                return
            
            positions = mt5.positions_get(ticket=ticket)
            if not positions:
                return
            
            position = positions[0]
            close_volume = position.volume * percentage
            
            request = {
                'action': mt5.TRADE_ACTION_DEAL,
                'symbol': position.symbol,
                'volume': close_volume,
                'type': mt5.ORDER_TYPE_SELL if position.type == 0 else mt5.ORDER_TYPE_BUY,
                'position': ticket,
                'magic': self.magic_number,
                'comment': f"Partial_Close_{percentage*100}%"
            }
            
            result = mt5.order_send(request)
            if result.retcode == mt5.TRADE_RETCODE_DONE:
                log_info(f"Partial close successful: {close_volume} lots")
            else:
                log_error(f"Partial close failed: {result.comment}")
                
        except Exception as e:
            log_error(f"Error in partial close: {e}")
    
    def _modify_stop_loss(self, ticket: int, new_sl: float):
        """Modify stop loss of a trade"""
        try:
            if self.paper_trading:
                log_info(f"PAPER TRADE: Modify SL to {new_sl} for trade {ticket}")
                return
            
            positions = mt5.positions_get(ticket=ticket)
            if not positions:
                return
            
            position = positions[0]
            
            request = {
                'action': mt5.TRADE_ACTION_SLTP,
                'symbol': position.symbol,
                'position': ticket,
                'sl': new_sl,
                'tp': position.tp
            }
            
            result = mt5.order_send(request)
            if result.retcode == mt5.TRADE_RETCODE_DONE:
                log_info(f"Stop loss modified to {new_sl}")
            else:
                log_error(f"SL modification failed: {result.comment}")
                
        except Exception as e:
            log_error(f"Error modifying stop loss: {e}")
    
    def _close_trade(self, ticket: int, reason: str):
        """Close a trade"""
        try:
            if self.paper_trading:
                price_data = mt5_connector.get_current_price(self.active_trades[ticket]['symbol'])
                current_price = price_data['bid'] if self.active_trades[ticket]['type'] == 'SELL' else price_data['ask']
                self._close_paper_trade(ticket, current_price, reason)
                return
            
            positions = mt5.positions_get(ticket=ticket)
            if not positions:
                return
            
            position = positions[0]
            
            request = {
                'action': mt5.TRADE_ACTION_DEAL,
                'symbol': position.symbol,
                'volume': position.volume,
                'type': mt5.ORDER_TYPE_SELL if position.type == 0 else mt5.ORDER_TYPE_BUY,
                'position': ticket,
                'magic': self.magic_number,
                'comment': f"Close_{reason}"
            }
            
            result = mt5.order_send(request)
            if result.retcode == mt5.TRADE_RETCODE_DONE:
                log_info(f"Trade {ticket} closed: {reason}")
                self._handle_trade_closure(ticket, self.active_trades[ticket])
            else:
                log_error(f"Trade close failed: {result.comment}")
                
        except Exception as e:
            log_error(f"Error closing trade: {e}")
    
    def _close_paper_trade(self, ticket: int, close_price: float, reason: str):
        """Close paper trade"""
        try:
            trade_info = self.active_trades[ticket]
            
            # Calculate P&L
            if trade_info['type'] == 'BUY':
                pnl_pips = (close_price - trade_info['entry_price']) * 10000
            else:
                pnl_pips = (trade_info['entry_price'] - close_price) * 10000
            
            log_info(f"PAPER TRADE CLOSED: {ticket} - {reason} - P&L: {pnl_pips:.1f} pips")
            
            self._handle_trade_closure(ticket, trade_info, close_price, pnl_pips)
            
        except Exception as e:
            log_error(f"Error closing paper trade: {e}")
    
    def _handle_trade_closure(self, ticket: int, trade_info: Dict[str, Any], 
                             close_price: float = None, pnl_pips: float = None):
        """Handle trade closure and update records"""
        try:
            # Move to history
            trade_record = trade_info.copy()
            trade_record['close_time'] = datetime.now()
            trade_record['close_price'] = close_price
            trade_record['pnl_pips'] = pnl_pips
            
            self.trade_history.append(trade_record)
            
            # Remove from active trades
            if ticket in self.active_trades:
                del self.active_trades[ticket]
            
            log_trade(
                action="CLOSE",
                symbol=trade_info['symbol'],
                lot_size=trade_info['lot_size'],
                price=close_price or 0,
                sl=trade_info['sl'],
                tp=trade_info.get('tp1', 0),
                pnl_pips=pnl_pips or 0
            )
            
        except Exception as e:
            log_error(f"Error handling trade closure: {e}")
    
    def _validate_trade_parameters(self, ai_decision: Dict[str, Any]) -> bool:
        """Validate AI decision parameters"""
        required_fields = ['entry', 'sl']
        
        for field in required_fields:
            if field not in ai_decision or ai_decision[field] <= 0:
                return False
        
        # Check risk-reward ratio
        entry = ai_decision['entry']
        sl = ai_decision['sl']
        tp1 = ai_decision.get('tp1', 0)
        
        if tp1 > 0:
            risk = abs(entry - sl)
            reward = abs(tp1 - entry)
            rr_ratio = reward / risk if risk > 0 else 0
            
            if rr_ratio < 1.0:  # Minimum 1:1 RR
                log_warning(f"Poor risk-reward ratio: {rr_ratio:.2f}")
                return False
        
        return True
    
    def get_trade_statistics(self) -> Dict[str, Any]:
        """Get trading statistics"""
        try:
            if not self.trade_history:
                return {'total_trades': 0}
            
            total_trades = len(self.trade_history)
            winning_trades = sum(1 for trade in self.trade_history if trade.get('pnl_pips', 0) > 0)
            losing_trades = total_trades - winning_trades
            
            win_rate = (winning_trades / total_trades) * 100 if total_trades > 0 else 0
            
            total_pips = sum(trade.get('pnl_pips', 0) for trade in self.trade_history)
            avg_win = sum(trade.get('pnl_pips', 0) for trade in self.trade_history if trade.get('pnl_pips', 0) > 0) / max(1, winning_trades)
            avg_loss = sum(trade.get('pnl_pips', 0) for trade in self.trade_history if trade.get('pnl_pips', 0) < 0) / max(1, losing_trades)
            
            return {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'losing_trades': losing_trades,
                'win_rate': round(win_rate, 2),
                'total_pips': round(total_pips, 1),
                'average_win': round(avg_win, 1),
                'average_loss': round(avg_loss, 1),
                'profit_factor': round(abs(avg_win / avg_loss), 2) if avg_loss != 0 else 0
            }
            
        except Exception as e:
            log_error(f"Error calculating statistics: {e}")
            return {'error': str(e)}

# Global trade executor instance
trade_executor = TradeExecutor()
