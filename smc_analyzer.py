"""
Smart Money Concepts (SMC) Analysis Engine
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime, timedelta
import smartmoneyconcepts as smc
from logger import log_info, log_error, log_debug, log_warning
from utils import calculate_distance_penalty, get_psychological_levels

class SMCAnalyzer:
    """Smart Money Concepts analysis class"""
    
    def __init__(self):
        self.cache = {}
        self.cache_timeout = 300  # 5 minutes
    
    def analyze_market_structure(self, df: pd.DataFrame, timeframe: str) -> Dict[str, Any]:
        """Analyze market structure for BOS/CHoCH detection"""
        try:
            # Ensure we have enough data
            if len(df) < 50:
                return {'structure': 'INSUFFICIENT_DATA', 'bos': [], 'choch': []}
            
            # Calculate swing highs and lows
            swing_highs = self._find_swing_points(df, 'high', window=5)
            swing_lows = self._find_swing_points(df, 'low', window=5)
            
            # Detect BOS (Break of Structure)
            bos_signals = self._detect_bos(df, swing_highs, swing_lows)
            
            # Detect CHoCH (Change of Character)
            choch_signals = self._detect_choch(df, swing_highs, swing_lows)
            
            # Determine overall structure
            recent_structure = self._determine_structure(bos_signals, choch_signals)
            
            return {
                'structure': recent_structure,
                'bos': bos_signals[-5:],  # Last 5 BOS
                'choch': choch_signals[-3:],  # Last 3 CHoCH
                'swing_highs': swing_highs[-10:],
                'swing_lows': swing_lows[-10:]
            }
            
        except Exception as e:
            log_error(f"Error analyzing market structure: {e}")
            return {'structure': 'ERROR', 'bos': [], 'choch': []}
    
    def detect_order_blocks(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Detect bullish and bearish order blocks"""
        try:
            order_blocks = []
            
            # Use smartmoneyconcepts library for order block detection
            ob_data = smc.ob(df, length=10)
            
            if ob_data is not None and not ob_data.empty:
                # Process bullish order blocks
                bullish_obs = ob_data[ob_data['OB'] == 1]
                for idx, row in bullish_obs.tail(5).iterrows():
                    order_blocks.append({
                        'type': 'BULLISH',
                        'time': idx,
                        'high': row['high'],
                        'low': row['low'],
                        'strength': self._calculate_ob_strength(df, idx, 'bullish'),
                        'tested': False,
                        'fresh': True
                    })
                
                # Process bearish order blocks
                bearish_obs = ob_data[ob_data['OB'] == -1]
                for idx, row in bearish_obs.tail(5).iterrows():
                    order_blocks.append({
                        'type': 'BEARISH',
                        'time': idx,
                        'high': row['high'],
                        'low': row['low'],
                        'strength': self._calculate_ob_strength(df, idx, 'bearish'),
                        'tested': False,
                        'fresh': True
                    })
            
            return sorted(order_blocks, key=lambda x: x['time'], reverse=True)
            
        except Exception as e:
            log_error(f"Error detecting order blocks: {e}")
            return []
    
    def detect_fair_value_gaps(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Detect Fair Value Gaps (FVG)"""
        try:
            fvgs = []
            
            # Use smartmoneyconcepts library for FVG detection
            fvg_data = smc.fvg(df)
            
            if fvg_data is not None and not fvg_data.empty:
                # Process bullish FVGs
                bullish_fvgs = fvg_data[fvg_data['FVG'] == 1]
                for idx, row in bullish_fvgs.tail(3).iterrows():
                    fvgs.append({
                        'type': 'BULLISH',
                        'time': idx,
                        'top': row['TopFVG'],
                        'bottom': row['BottomFVG'],
                        'filled': False,
                        'strength': self._calculate_fvg_strength(df, idx)
                    })
                
                # Process bearish FVGs
                bearish_fvgs = fvg_data[fvg_data['FVG'] == -1]
                for idx, row in bearish_fvgs.tail(3).iterrows():
                    fvgs.append({
                        'type': 'BEARISH',
                        'time': idx,
                        'top': row['TopFVG'],
                        'bottom': row['BottomFVG'],
                        'filled': False,
                        'strength': self._calculate_fvg_strength(df, idx)
                    })
            
            return sorted(fvgs, key=lambda x: x['time'], reverse=True)
            
        except Exception as e:
            log_error(f"Error detecting FVGs: {e}")
            return []
    
    def detect_supply_demand_zones(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Detect Supply and Demand zones"""
        try:
            zones = []
            
            # Rally-Base-Drop (Supply zones)
            supply_zones = self._detect_rbd_patterns(df)
            zones.extend(supply_zones)
            
            # Drop-Base-Rally (Demand zones)
            demand_zones = self._detect_dbr_patterns(df)
            zones.extend(demand_zones)
            
            # Score zones by strength
            for zone in zones:
                zone['strength'] = self._calculate_zone_strength(df, zone)
            
            return sorted(zones, key=lambda x: x['strength'], reverse=True)[:10]
            
        except Exception as e:
            log_error(f"Error detecting supply/demand zones: {e}")
            return []
    
    def detect_support_resistance(self, df: pd.DataFrame) -> Dict[str, List[float]]:
        """Detect Support and Resistance levels"""
        try:
            current_price = df['close'].iloc[-1]
            
            # Pivot points
            pivot_levels = self._calculate_pivot_points(df)
            
            # Psychological levels
            psych_levels = get_psychological_levels(
                symbol="EURUSD",  # Default, should be passed as parameter
                current_price=current_price,
                range_pips=100
            )
            
            # Historical levels
            historical_levels = self._find_historical_levels(df)
            
            # Combine and filter levels
            all_levels = pivot_levels + psych_levels + historical_levels
            
            # Separate into support and resistance
            support_levels = [level for level in all_levels if level < current_price]
            resistance_levels = [level for level in all_levels if level > current_price]
            
            # Sort and limit
            support_levels = sorted(support_levels, reverse=True)[:5]
            resistance_levels = sorted(resistance_levels)[:5]
            
            return {
                'support': support_levels,
                'resistance': resistance_levels,
                'current_price': current_price
            }
            
        except Exception as e:
            log_error(f"Error detecting support/resistance: {e}")
            return {'support': [], 'resistance': [], 'current_price': 0}
    
    def _find_swing_points(self, df: pd.DataFrame, column: str, window: int = 5) -> List[Dict]:
        """Find swing highs or lows"""
        swings = []
        
        for i in range(window, len(df) - window):
            if column == 'high':
                # Swing high
                if all(df[column].iloc[i] >= df[column].iloc[i-j] for j in range(1, window+1)) and \
                   all(df[column].iloc[i] >= df[column].iloc[i+j] for j in range(1, window+1)):
                    swings.append({
                        'time': df.index[i],
                        'price': df[column].iloc[i],
                        'type': 'HIGH'
                    })
            else:
                # Swing low
                if all(df[column].iloc[i] <= df[column].iloc[i-j] for j in range(1, window+1)) and \
                   all(df[column].iloc[i] <= df[column].iloc[i+j] for j in range(1, window+1)):
                    swings.append({
                        'time': df.index[i],
                        'price': df[column].iloc[i],
                        'type': 'LOW'
                    })
        
        return swings
    
    def _detect_bos(self, df: pd.DataFrame, swing_highs: List, swing_lows: List) -> List[Dict]:
        """Detect Break of Structure"""
        bos_signals = []
        
        # Simple BOS detection logic
        if len(swing_highs) >= 2 and len(swing_lows) >= 2:
            last_high = swing_highs[-1]['price']
            last_low = swing_lows[-1]['price']
            current_price = df['close'].iloc[-1]
            
            # Bullish BOS
            if current_price > last_high:
                bos_signals.append({
                    'type': 'BULLISH_BOS',
                    'time': df.index[-1],
                    'price': current_price,
                    'broken_level': last_high
                })
            
            # Bearish BOS
            if current_price < last_low:
                bos_signals.append({
                    'type': 'BEARISH_BOS',
                    'time': df.index[-1],
                    'price': current_price,
                    'broken_level': last_low
                })
        
        return bos_signals
    
    def _detect_choch(self, df: pd.DataFrame, swing_highs: List, swing_lows: List) -> List[Dict]:
        """Detect Change of Character"""
        choch_signals = []
        
        # Simplified CHoCH detection
        if len(swing_highs) >= 3 and len(swing_lows) >= 3:
            # Look for structure changes
            recent_highs = [h['price'] for h in swing_highs[-3:]]
            recent_lows = [l['price'] for l in swing_lows[-3:]]
            
            # Check for trend change patterns
            if len(recent_highs) >= 2 and recent_highs[-1] < recent_highs[-2]:
                choch_signals.append({
                    'type': 'BEARISH_CHOCH',
                    'time': df.index[-1],
                    'price': df['close'].iloc[-1]
                })
            
            if len(recent_lows) >= 2 and recent_lows[-1] > recent_lows[-2]:
                choch_signals.append({
                    'type': 'BULLISH_CHOCH',
                    'time': df.index[-1],
                    'price': df['close'].iloc[-1]
                })
        
        return choch_signals

    def _determine_structure(self, bos_signals: List, choch_signals: List) -> str:
        """Determine overall market structure"""
        if not bos_signals and not choch_signals:
            return 'NEUTRAL'

        # Get most recent signals
        recent_signals = []
        if bos_signals:
            recent_signals.extend(bos_signals[-2:])
        if choch_signals:
            recent_signals.extend(choch_signals[-2:])

        if not recent_signals:
            return 'NEUTRAL'

        # Sort by time
        recent_signals.sort(key=lambda x: x['time'], reverse=True)

        latest_signal = recent_signals[0]['type']
        if 'BULLISH' in latest_signal:
            return 'BULLISH'
        elif 'BEARISH' in latest_signal:
            return 'BEARISH'
        else:
            return 'NEUTRAL'

    def _calculate_ob_strength(self, df: pd.DataFrame, idx: int, ob_type: str) -> float:
        """Calculate order block strength (1-10)"""
        try:
            strength = 5.0  # Base strength

            # Volume factor (if available)
            if 'tick_volume' in df.columns:
                avg_volume = df['tick_volume'].rolling(20).mean().iloc[idx]
                current_volume = df['tick_volume'].iloc[idx]
                if current_volume > avg_volume * 1.5:
                    strength += 1.5

            # Size factor
            candle_size = abs(df['high'].iloc[idx] - df['low'].iloc[idx])
            avg_size = abs(df['high'] - df['low']).rolling(20).mean().iloc[idx]
            if candle_size > avg_size * 1.2:
                strength += 1.0

            # Reaction factor (how price reacted after)
            if idx < len(df) - 5:
                if ob_type == 'bullish':
                    reaction = df['high'].iloc[idx+1:idx+6].max() - df['close'].iloc[idx]
                else:
                    reaction = df['close'].iloc[idx] - df['low'].iloc[idx+1:idx+6].min()

                if reaction > candle_size * 0.5:
                    strength += 1.0

            return min(10.0, max(1.0, strength))

        except:
            return 5.0

    def _calculate_fvg_strength(self, df: pd.DataFrame, idx: int) -> float:
        """Calculate Fair Value Gap strength"""
        try:
            strength = 5.0

            # Gap size factor
            gap_size = abs(df['high'].iloc[idx-1] - df['low'].iloc[idx+1])
            avg_range = abs(df['high'] - df['low']).rolling(20).mean().iloc[idx]

            if gap_size > avg_range * 1.5:
                strength += 2.0
            elif gap_size > avg_range:
                strength += 1.0

            return min(10.0, max(1.0, strength))

        except:
            return 5.0

    def _detect_rbd_patterns(self, df: pd.DataFrame) -> List[Dict]:
        """Detect Rally-Base-Drop patterns (Supply zones)"""
        zones = []

        try:
            for i in range(20, len(df) - 10):
                # Look for rally phase
                rally_start = i - 15
                rally_end = i - 5
                rally_move = df['high'].iloc[rally_start:rally_end].max() - df['low'].iloc[rally_start:rally_end].min()

                # Look for base phase
                base_high = df['high'].iloc[i-5:i+1].max()
                base_low = df['low'].iloc[i-5:i+1].min()
                base_range = base_high - base_low

                # Look for drop phase
                drop_start = i + 1
                drop_end = min(i + 10, len(df))
                drop_move = df['high'].iloc[drop_start:drop_end].max() - df['low'].iloc[drop_start:drop_end].min()

                # Validate RBD pattern
                if (rally_move > base_range * 2 and
                    drop_move > base_range * 1.5 and
                    df['low'].iloc[drop_start:drop_end].min() < base_low):

                    zones.append({
                        'type': 'SUPPLY',
                        'time': df.index[i],
                        'high': base_high,
                        'low': base_low,
                        'tested': False,
                        'fresh': True
                    })

        except Exception as e:
            log_error(f"Error detecting RBD patterns: {e}")

        return zones

    def _detect_dbr_patterns(self, df: pd.DataFrame) -> List[Dict]:
        """Detect Drop-Base-Rally patterns (Demand zones)"""
        zones = []

        try:
            for i in range(20, len(df) - 10):
                # Look for drop phase
                drop_start = i - 15
                drop_end = i - 5
                drop_move = df['high'].iloc[drop_start:drop_end].max() - df['low'].iloc[drop_start:drop_end].min()

                # Look for base phase
                base_high = df['high'].iloc[i-5:i+1].max()
                base_low = df['low'].iloc[i-5:i+1].min()
                base_range = base_high - base_low

                # Look for rally phase
                rally_start = i + 1
                rally_end = min(i + 10, len(df))
                rally_move = df['high'].iloc[rally_start:rally_end].max() - df['low'].iloc[rally_start:rally_end].min()

                # Validate DBR pattern
                if (drop_move > base_range * 2 and
                    rally_move > base_range * 1.5 and
                    df['high'].iloc[rally_start:rally_end].max() > base_high):

                    zones.append({
                        'type': 'DEMAND',
                        'time': df.index[i],
                        'high': base_high,
                        'low': base_low,
                        'tested': False,
                        'fresh': True
                    })

        except Exception as e:
            log_error(f"Error detecting DBR patterns: {e}")

        return zones

    def _calculate_zone_strength(self, df: pd.DataFrame, zone: Dict) -> float:
        """Calculate supply/demand zone strength"""
        try:
            strength = 5.0

            # Time factor (fresher zones are stronger)
            zone_time = zone['time']
            current_time = df.index[-1]
            time_diff = (current_time - zone_time).total_seconds() / 3600  # Hours

            if time_diff < 24:
                strength += 2.0
            elif time_diff < 72:
                strength += 1.0

            # Size factor
            zone_size = zone['high'] - zone['low']
            avg_range = abs(df['high'] - df['low']).rolling(20).mean().iloc[-1]

            if zone_size < avg_range * 0.5:
                strength += 1.5  # Tight zones are stronger

            # Test factor
            if not zone['tested']:
                strength += 1.0

            return min(10.0, max(1.0, strength))

        except:
            return 5.0

    def _calculate_pivot_points(self, df: pd.DataFrame) -> List[float]:
        """Calculate pivot points"""
        try:
            # Use last complete day's data
            yesterday_high = df['high'].iloc[-24:-1].max() if len(df) > 24 else df['high'].max()
            yesterday_low = df['low'].iloc[-24:-1].min() if len(df) > 24 else df['low'].min()
            yesterday_close = df['close'].iloc[-24] if len(df) > 24 else df['close'].iloc[-1]

            # Calculate pivot point
            pp = (yesterday_high + yesterday_low + yesterday_close) / 3

            # Calculate support and resistance levels
            r1 = 2 * pp - yesterday_low
            r2 = pp + (yesterday_high - yesterday_low)
            s1 = 2 * pp - yesterday_high
            s2 = pp - (yesterday_high - yesterday_low)

            return [s2, s1, pp, r1, r2]

        except:
            return []

    def _find_historical_levels(self, df: pd.DataFrame) -> List[float]:
        """Find historical support/resistance levels"""
        levels = []

        try:
            # Find significant highs and lows
            highs = df['high'].rolling(window=10, center=True).max()
            lows = df['low'].rolling(window=10, center=True).min()

            # Get levels that were tested multiple times
            for i in range(10, len(df) - 10):
                high_level = highs.iloc[i]
                low_level = lows.iloc[i]

                # Count touches within small range
                high_touches = sum(1 for h in df['high'].iloc[i-10:i+10]
                                 if abs(h - high_level) < high_level * 0.001)
                low_touches = sum(1 for l in df['low'].iloc[i-10:i+10]
                                if abs(l - low_level) < low_level * 0.001)

                if high_touches >= 3:
                    levels.append(high_level)
                if low_touches >= 3:
                    levels.append(low_level)

            return list(set(levels))  # Remove duplicates

        except:
            return []

# Global SMC analyzer instance
smc_analyzer = SMCAnalyzer()
